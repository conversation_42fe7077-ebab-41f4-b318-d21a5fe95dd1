#!/usr/bin/env python3
"""
命令行界面
简化的用户交互界面
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir.parent))

from java_code_visualizer.main import JavaCodeVisualizer


def interactive_mode():
    """交互式模式"""
    print("=== Java代码结构可视化工具 - 交互模式 ===\n")
    
    visualizer = JavaCodeVisualizer()
    
    # 获取项目路径
    while True:
        project_path = input("请输入Java项目路径（目录或ZIP文件）: ").strip()
        if os.path.exists(project_path):
            break
        print("路径不存在，请重新输入")
    
    # 加载项目
    print(f"\n正在加载项目: {project_path}")
    if not visualizer.parser.load_project(project_path):
        print("项目加载失败")
        return
    
    # 显示可用的类
    print(f"\n找到 {len(visualizer.parser.classes)} 个类:")
    for i, (class_name, class_info) in enumerate(visualizer.parser.classes.items(), 1):
        print(f"{i:2d}. {class_name} ({len(class_info.methods)} 个方法)")
    
    # 获取目标文件和方法
    while True:
        target_file = input("\n请输入目标Java文件的相对路径: ").strip()
        target_method = input("请输入目标方法名: ").strip()
        
        if target_file and target_method:
            break
        print("请输入有效的文件路径和方法名")
    
    # 获取输出路径
    output_path = input("请输入输出文件路径（可选，按回车使用默认）: ").strip()
    if not output_path:
        safe_method_name = target_method.replace('<', '_').replace('>', '_')
        output_path = f"output/flowchart_{safe_method_name}"
    
    # 执行分析
    print(f"\n开始分析...")
    success = visualizer.analyze_project(project_path, target_file, target_method, output_path)
    
    if success:
        print("\n✅ 分析完成！")
        input("按回车键退出...")
    else:
        print("\n❌ 分析失败！")
        input("按回车键退出...")


def quick_mode():
    """快速模式（命令行参数）"""
    if len(sys.argv) < 4:
        print("用法: python cli.py <项目路径> <目标文件> <目标方法> [输出路径]")
        print("示例: python cli.py ./my-spring-project src/main/java/com/example/service/UserService.java getUserById")
        return
    
    project_path = sys.argv[1]
    target_file = sys.argv[2]
    target_method = sys.argv[3]
    output_path = sys.argv[4] if len(sys.argv) > 4 else None
    
    visualizer = JavaCodeVisualizer()
    success = visualizer.analyze_project(project_path, target_file, target_method, output_path)
    
    if not success:
        sys.exit(1)


def main():
    """主入口"""
    if len(sys.argv) == 1 or (len(sys.argv) == 2 and sys.argv[1] in ['-i', '--interactive']):
        interactive_mode()
    else:
        quick_mode()


if __name__ == '__main__':
    main()
