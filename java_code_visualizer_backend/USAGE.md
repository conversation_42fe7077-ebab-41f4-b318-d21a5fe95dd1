# Java代码结构可视化工具 - 使用指南

## 🎯 工具概述

这是一个专门用于分析Spring Boot项目方法调用链的Python工具。它能够：
- 解析Java源代码，构建抽象语法树(AST)
- 分析方法调用关系，包括继承、接口、重载和多态
- 过滤掉JDK、Spring框架等非业务逻辑方法
- 生成清晰的可视化流程图

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install javalang graphviz networkx matplotlib
```

**重要**: 还需要安装Graphviz系统工具：
- Windows: 从 https://graphviz.org/download/ 下载安装
- macOS: `brew install graphviz`
- Linux: `sudo apt-get install graphviz`

### 2. 验证安装

```bash
python java_code_visualizer/quick_test.py
```

### 3. 运行示例

```bash
python java_code_visualizer/run.py --sample
```

## 📖 详细使用方法

### 交互模式（推荐新手）

```bash
python java_code_visualizer/run.py
```

按照提示输入：
1. Spring Boot项目路径（目录或ZIP文件）
2. 目标Java文件的相对路径
3. 要分析的方法名
4. 输出文件路径（可选）

### 命令行模式（推荐脚本使用）

```bash
python java_code_visualizer/run.py <项目路径> <目标文件> <目标方法> [输出路径]
```

**示例**：
```bash
# 分析UserService中的getUserById方法
python java_code_visualizer/run.py \
    ./my-spring-project \
    src/main/java/com/example/service/UserService.java \
    getUserById \
    output/user_analysis

# 分析OrderController中的createOrder方法
python java_code_visualizer/run.py \
    ./ecommerce-project.zip \
    src/main/java/com/shop/controller/OrderController.java \
    createOrder
```

## 📁 输入格式说明

### 项目路径
- **目录路径**: Spring Boot项目的根目录（包含src/main/java）
- **ZIP文件**: 完整的项目ZIP包

### 目标文件路径
相对于项目根目录的Java文件路径，例如：
- `src/main/java/com/example/service/UserService.java`
- `src/main/java/com/company/module/BusinessLogic.java`

### 方法名
要分析的方法名称，例如：
- `getUserById`
- `processOrder`
- `calculateTotal`

## 📊 输出文件说明

工具会生成以下文件：

### 1. 流程图文件
- **PNG格式**: `output.png` (需要Graphviz)
- **SVG格式**: `output.svg` (可缩放，需要Graphviz)
- **文本格式**: `output_text.txt` (DOT源代码，无需Graphviz)

### 2. 分析报告
- **报告文件**: `output_report.txt`
- 包含统计信息和详细调用路径

### 3. DOT源文件
- **DOT文件**: `output.dot` (Graphviz源代码)

## 🔍 分析结果解读

### 流程图元素

**节点（方法）**：
- 蓝色：普通方法
- 绿色：静态方法
- 黄色：抽象方法
- 橙色：检测到循环的方法

**边（调用关系）**：
- 实线：直接调用
- 虚线：接口调用
- 点线：继承调用
- 粗线：多态调用

### 报告内容

```
=== 方法调用链分析报告 ===

总调用路径数: 5        # 从目标方法开始的所有调用路径
最大调用深度: 3        # 最深的调用层级
涉及方法数: 8          # 参与调用的方法总数
检测到循环: 1          # 循环调用的数量

=== 详细调用路径 ===

路径 1 (深度: 3):
  ├─ UserService.processUserLogin
    ├─ UserService.validateCredentials
      └─ UserService.checkPassword
```

## ⚙️ 高级功能

### 1. 列出项目中的所有类

```bash
python java_code_visualizer/run.py --list-classes <项目路径>
```

### 2. 生成多种格式

工具自动生成PNG、SVG、DOT等多种格式的输出文件。

### 3. 处理复杂项目结构

工具能够处理：
- 多模块Spring Boot项目
- 复杂的继承层次
- 接口的多个实现
- 方法重载
- 循环调用检测

## 🛠️ 故障排除

### 常见问题

**1. "未找到方法"**
- 检查文件路径是否正确（相对于项目根目录）
- 检查方法名是否拼写正确
- 使用 `--list-classes` 查看可用的类和方法

**2. "项目加载失败"**
- 确保项目包含Java源文件
- 检查项目结构是否为标准的Maven/Gradle结构
- 确保文件编码为UTF-8

**3. "渲染失败"**
- 安装Graphviz系统工具
- 检查PATH环境变量是否包含Graphviz的bin目录
- 工具会自动生成文本版本作为备选

**4. "分析时间过长"**
- 大型项目可能需要几分钟时间
- 可以先分析小的方法进行测试

### 调试模式

如果遇到问题，可以：

1. 运行快速测试：
```bash
python java_code_visualizer/quick_test.py
```

2. 查看详细错误信息：
```bash
python java_code_visualizer/debug_test.py
```

## 📝 最佳实践

### 1. 选择合适的分析目标
- 选择业务逻辑的入口方法（如Controller的处理方法）
- 避免分析getter/setter等简单方法
- 优先分析Service层的核心业务方法

### 2. 项目准备
- 确保项目能够编译通过
- 移除不必要的测试文件和示例代码
- 确保包结构清晰

### 3. 结果分析
- 关注调用深度较深的路径
- 注意循环调用的警告
- 结合业务逻辑理解调用关系

## 🔧 自定义配置

可以修改 `config.py` 文件来自定义：
- 排除的包列表
- 图形样式配置
- 递归深度限制
- 支持的文件扩展名

## 📞 技术支持

如果遇到问题：
1. 查看生成的错误日志
2. 检查项目结构是否符合标准
3. 确认所有依赖都已正确安装
4. 尝试使用示例项目进行测试
