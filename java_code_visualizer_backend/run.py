#!/usr/bin/env python3
"""
启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from java_code_visualizer.cli import interactive_mode, main
from java_code_visualizer.main import JavaCodeVisualizer


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['javalang', 'graphviz', 'networkx']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install javalang graphviz networkx matplotlib")
        return False
    
    return True


def show_help():
    """显示帮助信息"""
    help_text = """
=== Java代码结构可视化工具 ===

用法:
1. 交互模式:
   python run.py
   或
   python run.py -i

2. 命令行模式:
   python run.py <项目路径> <目标文件> <目标方法> [输出路径]

3. 示例:
   python run.py ./my-spring-project src/main/java/com/example/service/UserService.java getUserById

4. 运行测试:
   python run.py --test

5. 运行示例分析:
   python run.py --sample

参数说明:
- 项目路径: Spring Boot项目的根目录或ZIP文件路径
- 目标文件: Java文件的相对路径（从src/main/java开始）
- 目标方法: 要分析的方法名
- 输出路径: 生成的流程图文件路径（可选）

功能特点:
✅ 解析Java源代码，构建AST
✅ 分析方法调用关系
✅ 处理继承、接口、重载和多态
✅ 过滤JDK和框架方法，专注业务逻辑
✅ 生成可视化流程图
✅ 检测循环调用
✅ 支持Spring Boot项目特性
"""
    print(help_text)


def main_entry():
    """主入口函数"""
    # 检查参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['-h', '--help']:
            show_help()
            return
        elif sys.argv[1] == '--test':
            from java_code_visualizer.test_sample import TestJavaCodeVisualizer
            import unittest
            unittest.main(module='java_code_visualizer.test_sample', argv=[''])
            return
        elif sys.argv[1] == '--sample':
            from java_code_visualizer.test_sample import run_sample_analysis
            run_sample_analysis()
            return
        elif sys.argv[1] in ['-i', '--interactive']:
            if not check_dependencies():
                return
            interactive_mode()
            return
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 根据参数数量决定模式
    if len(sys.argv) == 1:
        interactive_mode()
    elif len(sys.argv) >= 4:
        # 命令行模式
        project_path = sys.argv[1]
        target_file = sys.argv[2]
        target_method = sys.argv[3]
        output_path = sys.argv[4] if len(sys.argv) > 4 else None
        
        visualizer = JavaCodeVisualizer()
        success = visualizer.analyze_project(project_path, target_file, target_method, output_path)
        
        if not success:
            sys.exit(1)
    else:
        show_help()


if __name__ == '__main__':
    main_entry()
