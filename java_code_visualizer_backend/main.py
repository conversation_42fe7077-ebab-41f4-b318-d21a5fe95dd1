"""
主程序入口
提供CLI界面用于Java代码结构可视化
"""

import argparse
import os
import sys
from pathlib import Path
import time

try:
    from .java_parser import JavaProjectParser
    from .call_chain_analyzer import CallChainAnalyzer
    from .flowchart_generator import FlowchartGenerator, InteractiveFlowchartGenerator
    from .filter import CallFilter, ProjectPackageDetector, SpringBootMethodFilter
except ImportError:
    # 支持直接运行
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent))

    from java_parser import JavaProjectParser
    from call_chain_analyzer import CallChainAnalyzer
    from flowchart_generator import FlowchartGenerator, InteractiveFlowchartGenerator
    from filter import CallFilter, ProjectPackageDetector, SpringBootMethodFilter


class JavaCodeVisualizer:
    """Java代码可视化工具主类"""
    
    def __init__(self):
        self.parser = JavaProjectParser()
        self.analyzer = None
        self.filter = None
        self.generator = None
        self.spring_filter = SpringBootMethodFilter()
    
    def analyze_project(self, project_path: str, target_file: str, target_method: str, 
                       output_path: str = None) -> bool:
        """分析项目并生成可视化图"""
        try:
            print("=== Java代码结构可视化工具 ===\n")
            
            # 1. 加载项目
            print(f"正在加载项目: {project_path}")
            start_time = time.time()
            
            if not self.parser.load_project(project_path):
                print("项目加载失败")
                return False
            
            load_time = time.time() - start_time
            print(f"项目加载完成，耗时: {load_time:.2f}秒\n")
            
            # 2. 检测项目包
            project_packages = ProjectPackageDetector.detect_project_packages(self.parser.classes)
            print(f"检测到项目包: {', '.join(project_packages)}")
            
            # 3. 初始化过滤器和分析器
            self.filter = CallFilter(project_packages)
            self.analyzer = CallChainAnalyzer(self.parser)
            self.generator = FlowchartGenerator(self.analyzer)
            
            # 4. 构建调用图
            print("正在构建调用图...")
            self.analyzer.build_call_graph()
            
            # 5. 查找目标方法
            print(f"正在查找方法: {target_file}#{target_method}")
            target_method_info = self.parser.get_method_by_signature(target_file, target_method)
            
            if not target_method_info:
                print(f"未找到指定方法: {target_file}#{target_method}")
                self._list_available_methods(target_file)
                return False
            
            print(f"找到目标方法: {target_method_info.class_name}.{target_method_info.method_name}")
            
            # 6. 提取调用链
            print("正在分析调用链...")
            call_paths = self.analyzer.extract_call_chain(target_file, target_method)
            
            if not call_paths:
                print("未找到调用链")
                return False
            
            print(f"找到 {len(call_paths)} 条调用路径")
            
            # 7. 生成流程图
            if not output_path:
                output_path = f"output/{target_method_info.class_name}_{target_method_info.method_name}_flowchart"
            
            print(f"正在生成流程图: {output_path}")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path) if os.path.dirname(output_path) else "output", exist_ok=True)
            
            output_file = self.generator.generate_flowchart(
                call_paths, 
                output_path,
                f"{target_method_info.class_name}.{target_method_info.method_name} Call Chain"
            )
            
            # 8. 生成报告
            report = self.generator.generate_summary_report(call_paths)
            report_file = f"{output_path}_report.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"\n=== 分析完成 ===")
            print(f"流程图: {output_file}")
            print(f"分析报告: {report_file}")
            print(f"\n{report}")
            
            return True
            
        except Exception as e:
            print(f"分析过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _list_available_methods(self, target_file: str):
        """列出指定文件中的可用方法"""
        print("\n可用的方法:")
        
        normalized_path = target_file.replace('\\', '/').replace('.java', '')
        
        found_methods = []
        for method_key, method_info in self.parser.methods.items():
            method_file_path = method_info.file_path.replace('\\', '/').replace('.java', '')
            if normalized_path in method_file_path or method_file_path.endswith(normalized_path):
                found_methods.append(method_info)
        
        if found_methods:
            for method in found_methods:
                params = ', '.join(method.parameters) if method.parameters else ''
                print(f"  - {method.method_name}({params}) : {method.return_type}")
        else:
            print("  未找到任何方法")
    
    def list_all_classes(self):
        """列出所有解析的类"""
        print("=== 项目中的所有类 ===")
        for class_name, class_info in self.parser.classes.items():
            class_type = "接口" if class_info.is_interface else "类"
            print(f"{class_type}: {class_name}")
            print(f"  文件: {class_info.file_path}")
            print(f"  方法数: {len(class_info.methods)}")
            if class_info.extends:
                print(f"  继承: {class_info.extends}")
            if class_info.implements:
                print(f"  实现: {', '.join(class_info.implements)}")
            print()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Java代码结构可视化工具')
    parser.add_argument('project_path', help='Java项目路径（目录或ZIP文件）')
    parser.add_argument('target_file', help='目标Java文件的相对路径')
    parser.add_argument('target_method', help='目标方法名')
    parser.add_argument('-o', '--output', help='输出文件路径（不含扩展名）')
    parser.add_argument('--list-classes', action='store_true', help='列出所有类')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    visualizer = JavaCodeVisualizer()
    
    if args.list_classes:
        if visualizer.parser.load_project(args.project_path):
            visualizer.list_all_classes()
        return
    
    # 分析项目
    success = visualizer.analyze_project(
        args.project_path,
        args.target_file,
        args.target_method,
        args.output
    )
    
    if success:
        print("\n分析完成！")
    else:
        print("\n分析失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
