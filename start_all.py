#!/usr/bin/env python3
"""
一键启动脚本 - 同时启动前后端服务
"""

import os
import sys
import subprocess
import threading
import time
import webbrowser
from pathlib import Path

def start_backend():
    """启动后端服务"""
    print("🔧 启动后端API服务...")
    try:
        subprocess.run([sys.executable, 'start_backend.py'])
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")

def start_frontend():
    """启动前端服务"""
    print("🌐 启动前端应用...")
    try:
        # 等待后端启动
        time.sleep(3)
        subprocess.run([sys.executable, 'start_frontend.py'])
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")

def check_environment():
    """检查运行环境"""
    print("=== Java代码结构可视化工具 - 一键启动 ===\n")
    print("🔍 检查运行环境...")
    
    # 检查Python
    python_version = sys.version.split()[0]
    print(f"✅ Python版本: {python_version}")
    
    # 检查必要文件
    required_files = [
        'backend/app.py',
        'frontend/package.json',
        'start_backend.py',
        'start_frontend.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 文件检查通过")
    return True

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(8)  # 等待服务启动
    print("\n🌐 正在打开浏览器...")
    webbrowser.open('http://localhost:3000')

def main():
    """主函数"""
    if not check_environment():
        input("按回车键退出...")
        return
    
    print("\n🚀 正在启动前后端服务...")
    print("📡 后端API: http://localhost:5001")
    print("🌐 前端应用: http://localhost:3000")
    print("⏹️  按 Ctrl+C 停止所有服务\n")
    
    try:
        # 在新线程中打开浏览器
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 在新线程中启动后端
        backend_thread = threading.Thread(target=start_backend)
        backend_thread.daemon = True
        backend_thread.start()
        
        # 在主线程中启动前端（这样可以接收Ctrl+C）
        start_frontend()
        
    except KeyboardInterrupt:
        print("\n👋 正在停止所有服务...")
        print("✅ 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
