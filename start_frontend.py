#!/usr/bin/env python3
"""
前端应用启动脚本
"""

import os
import subprocess
import platform





def get_npm_command():
    """获取适合当前系统的npm命令"""
    if platform.system() == "Windows":
        return "npm.cmd"
    return "npm"

def main():
    try:
        # 检查是否在正确的目录
        if not os.path.exists('frontend'):
            print("❌ 错误: 找不到frontend目录")
            print("请确保在项目根目录下运行此脚本")
            input("按回车键退出...")
            return

        # 检查frontend目录中是否有package.json
        if not os.path.exists('frontend/package.json'):
            print("❌ 错误: frontend目录中找不到package.json文件")
            input("按回车键退出...")
            return

        # 启动前端开发服务器
        print("\n🚀 正在启动前端开发服务器...")
        print("🌐 前端地址: http://localhost:3000")
        print("🔧 开发模式已启用")
        print("⏹️  按 Ctrl+C 停止服务器\n")



        # 切换到frontend目录并启动开发服务器
        frontend_dir = os.path.join(os.getcwd(), 'frontend')
        npm_cmd = get_npm_command()

        # 使用shell=True在Windows上运行npm命令
        if platform.system() == "Windows":
            subprocess.run([npm_cmd, 'run', 'dev'], cwd=frontend_dir, shell=True)
        else:
            subprocess.run([npm_cmd, 'run', 'dev'], cwd=frontend_dir)

    except KeyboardInterrupt:
        print("\n👋 前端服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print(f"错误详情: {type(e).__name__}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
