"""
调用链分析引擎
分析方法调用关系，处理继承、接口、重载和多态
"""

import networkx as nx
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass

from .java_parser import JavaProjectParser, MethodInfo, MethodCall, ClassInfo
from .config import MAX_RECURSION_DEPTH


@dataclass
class CallPath:
    """调用路径"""
    source_method: str
    target_method: str
    path: List[str]
    call_types: List[str]
    depth: int


class CallChainAnalyzer:
    """调用链分析器"""
    
    def __init__(self, parser: JavaProjectParser):
        self.parser = parser
        self.call_graph = nx.DiGraph()
        self.visited_methods: Set[str] = set()
        self.recursion_stack: Set[str] = set()
        
    def build_call_graph(self):
        """构建完整的调用图"""
        print("构建调用图...")
        
        # 添加所有方法作为节点
        for method_key, method_info in self.parser.methods.items():
            self.call_graph.add_node(method_key, method_info=method_info)
        
        # 添加调用关系作为边
        for method_key, method_info in self.parser.methods.items():
            for call in method_info.calls:
                target_methods = self._resolve_method_call(call)
                
                for target_method in target_methods:
                    if self.parser.is_project_method(call.called_class):
                        edge_data = {
                            'call_info': call,
                            'call_type': call.call_type
                        }
                        self.call_graph.add_edge(method_key, target_method, **edge_data)
        
        print(f"调用图构建完成: {self.call_graph.number_of_nodes()} 个节点, {self.call_graph.number_of_edges()} 条边")
    
    def _resolve_method_call(self, call: MethodCall) -> List[str]:
        """解析方法调用，处理多态和重载"""
        target_methods = []

        # 直接调用
        direct_method = f"{call.called_class}.{call.called_method}"
        if direct_method in self.parser.methods:
            target_methods.append(direct_method)

        # 处理接口调用
        target_methods.extend(self._resolve_interface_calls(call))

        # 处理继承调用
        target_methods.extend(self._resolve_inheritance_calls(call))

        # 处理重载
        target_methods.extend(self._resolve_overloaded_calls(call))

        # 如果没有找到任何匹配的方法，返回原始调用
        if not target_methods:
            target_methods.append(direct_method)

        return list(set(target_methods))  # 去重
    
    def _resolve_interface_calls(self, call: MethodCall) -> List[str]:
        """解析接口调用"""
        implementations = []
        
        # 查找实现了该接口的所有类
        for class_name, class_info in self.parser.classes.items():
            if call.called_class in class_info.implements:
                method_key = f"{class_info.name}.{call.called_method}"
                if method_key in self.parser.methods:
                    implementations.append(method_key)
        
        return implementations
    
    def _resolve_inheritance_calls(self, call: MethodCall) -> List[str]:
        """解析继承调用"""
        inherited_methods = []
        
        # 查找继承了该类的所有子类
        for class_name, class_info in self.parser.classes.items():
            if class_info.extends == call.called_class:
                method_key = f"{class_info.name}.{call.called_method}"
                if method_key in self.parser.methods:
                    inherited_methods.append(method_key)
        
        return inherited_methods
    
    def _resolve_overloaded_calls(self, call: MethodCall) -> List[str]:
        """解析重载方法调用"""
        overloaded_methods = []
        
        # 查找同名但参数不同的方法
        for method_key, method_info in self.parser.methods.items():
            if (method_info.class_name == call.called_class and 
                method_info.method_name == call.called_method and
                method_key not in [f"{call.called_class}.{call.called_method}"]):
                
                # 简单的参数匹配（可以进一步优化）
                if self._parameters_match(call.parameters, method_info.parameters):
                    overloaded_methods.append(method_key)
        
        return overloaded_methods
    
    def _parameters_match(self, call_params: List[str], method_params: List[str]) -> bool:
        """简单的参数匹配逻辑"""
        if len(call_params) != len(method_params):
            return False
        
        # 简化匹配：如果参数数量相同就认为匹配
        # 实际项目中需要更复杂的类型推断
        return True
    
    def extract_call_chain(self, file_path: str, method_name: str) -> List[CallPath]:
        """提取指定方法的调用链"""
        # 查找目标方法
        target_method = self.parser.get_method_by_signature(file_path, method_name)
        if not target_method:
            raise ValueError(f"未找到方法: {file_path}#{method_name}")
        
        method_key = f"{target_method.class_name}.{target_method.method_name}"
        
        # 重置状态
        self.visited_methods.clear()
        self.recursion_stack.clear()
        
        # 提取调用路径
        call_paths = []
        self._dfs_extract_calls(method_key, [], [], 0, call_paths)
        
        return call_paths
    
    def _dfs_extract_calls(self, current_method: str, path: List[str], call_types: List[str], 
                          depth: int, call_paths: List[CallPath]):
        """深度优先搜索提取调用链"""
        if depth > MAX_RECURSION_DEPTH:
            return
        
        if current_method in self.recursion_stack:
            # 检测到循环
            call_paths.append(CallPath(
                source_method=path[0] if path else current_method,
                target_method=current_method,
                path=path + [current_method, "CYCLE_DETECTED"],
                call_types=call_types + ["cycle"],
                depth=depth
            ))
            return
        
        self.recursion_stack.add(current_method)
        current_path = path + [current_method]
        
        # 获取当前方法的所有调用
        if current_method in self.parser.methods:
            method_info = self.parser.methods[current_method]
            
            if not method_info.calls:
                # 叶子节点
                call_paths.append(CallPath(
                    source_method=current_path[0],
                    target_method=current_method,
                    path=current_path,
                    call_types=call_types,
                    depth=depth
                ))
            else:
                # 继续递归
                for call in method_info.calls:
                    target_methods = self._resolve_method_call(call)
                    
                    for target_method in target_methods:
                        if self.parser.is_project_method(call.called_class):
                            self._dfs_extract_calls(
                                target_method,
                                current_path,
                                call_types + [call.call_type],
                                depth + 1,
                                call_paths
                            )
        
        self.recursion_stack.remove(current_method)
    
    def get_method_dependencies(self, method_key: str) -> Set[str]:
        """获取方法的所有依赖"""
        if method_key not in self.call_graph:
            return set()
        
        return set(self.call_graph.successors(method_key))
    
    def get_method_dependents(self, method_key: str) -> Set[str]:
        """获取依赖该方法的所有方法"""
        if method_key not in self.call_graph:
            return set()
        
        return set(self.call_graph.predecessors(method_key))
