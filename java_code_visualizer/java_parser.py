"""
Java代码解析模块
用于解析Java源文件，构建AST和方法调用关系
"""

import os
import javalang
from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import zipfile
import tempfile
import shutil

from .config import EXCLUDED_PACKAGES, SPRING_ANNOTATIONS, SUPPORTED_EXTENSIONS


@dataclass
class MethodInfo:
    """方法信息"""
    class_name: str
    method_name: str
    parameters: List[str]
    return_type: str
    is_static: bool
    is_abstract: bool
    file_path: str
    line_number: int
    calls: List['MethodCall']  # 该方法调用的其他方法


@dataclass
class MethodCall:
    """方法调用信息"""
    caller_class: str
    caller_method: str
    called_class: str
    called_method: str
    parameters: List[str]
    line_number: int
    call_type: str  # 'direct', 'interface', 'inheritance', 'polymorphic'


@dataclass
class ClassInfo:
    """类信息"""
    name: str
    package: str
    file_path: str
    is_interface: bool
    is_abstract: bool
    extends: Optional[str]
    implements: List[str]
    methods: Dict[str, MethodInfo]


class JavaProjectParser:
    """Java项目解析器"""
    
    def __init__(self):
        self.classes: Dict[str, ClassInfo] = {}
        self.methods: Dict[str, MethodInfo] = {}
        self.project_root: str = ""
        self.package_mapping: Dict[str, str] = {}  # 类名到完整包名的映射
        
    def load_project(self, project_path: str) -> bool:
        """
        加载Java项目
        支持目录路径或ZIP文件
        """
        try:
            if project_path.endswith('.zip'):
                return self._load_from_zip(project_path)
            else:
                return self._load_from_directory(project_path)
        except Exception as e:
            print(f"加载项目失败: {e}")
            return False
    
    def _load_from_zip(self, zip_path: str) -> bool:
        """从ZIP文件加载项目"""
        temp_dir = tempfile.mkdtemp()
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 查找项目根目录（通常包含src目录）
            for root, dirs, files in os.walk(temp_dir):
                if 'src' in dirs:
                    self.project_root = root
                    break
            else:
                self.project_root = temp_dir
                
            return self._parse_java_files()
        except Exception as e:
            print(f"解压ZIP文件失败: {e}")
            return False
        finally:
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
    
    def _load_from_directory(self, dir_path: str) -> bool:
        """从目录加载项目"""
        if not os.path.exists(dir_path):
            print(f"目录不存在: {dir_path}")
            return False
            
        self.project_root = dir_path
        return self._parse_java_files()
    
    def _parse_java_files(self) -> bool:
        """解析所有Java文件"""
        java_files = []
        
        # 递归查找所有Java文件
        for root, dirs, files in os.walk(self.project_root):
            for file in files:
                if file.endswith('.java'):
                    java_files.append(os.path.join(root, file))
        
        if not java_files:
            print("未找到Java文件")
            return False
        
        print(f"找到 {len(java_files)} 个Java文件")
        
        # 解析每个Java文件
        for java_file in java_files:
            try:
                self._parse_single_file(java_file)
            except Exception as e:
                print(f"解析文件 {java_file} 失败: {e}")
                continue
        
        print(f"成功解析 {len(self.classes)} 个类")
        return True
    
    def _parse_single_file(self, file_path: str):
        """解析单个Java文件"""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        try:
            tree = javalang.parse.parse(content)
        except Exception as e:
            print(f"解析AST失败 {file_path}: {e}")
            return
        
        # 获取包名
        package_name = tree.package.name if tree.package else ""
        
        # 解析类和接口
        for path, node in tree.filter(javalang.tree.ClassDeclaration):
            self._parse_class(node, package_name, file_path, False)
        
        for path, node in tree.filter(javalang.tree.InterfaceDeclaration):
            self._parse_class(node, package_name, file_path, True)
    
    def _parse_class(self, node, package_name: str, file_path: str, is_interface: bool):
        """解析类或接口"""
        class_name = node.name
        full_class_name = f"{package_name}.{class_name}" if package_name else class_name
        
        # 获取继承和实现信息
        extends = None
        implements = []
        
        if hasattr(node, 'extends') and node.extends:
            extends = node.extends.name
        
        if hasattr(node, 'implements') and node.implements:
            implements = [impl.name for impl in node.implements]
        
        # 创建类信息
        class_info = ClassInfo(
            name=class_name,
            package=package_name,
            file_path=file_path,
            is_interface=is_interface,
            is_abstract=self._is_abstract(node),
            extends=extends,
            implements=implements,
            methods={}
        )
        
        # 解析方法
        for method_node in node.methods:
            method_info = self._parse_method(method_node, class_name, file_path)
            if method_info:
                method_key = f"{class_name}.{method_info.method_name}"
                class_info.methods[method_key] = method_info
                self.methods[method_key] = method_info
        
        self.classes[full_class_name] = class_info
        self.package_mapping[class_name] = full_class_name
    
    def _parse_method(self, method_node, class_name: str, file_path: str) -> Optional[MethodInfo]:
        """解析方法"""
        try:
            method_name = method_node.name
            
            # 获取参数类型
            parameters = []
            if method_node.parameters:
                for param in method_node.parameters:
                    param_type = self._get_type_name(param.type)
                    parameters.append(param_type)
            
            # 获取返回类型
            return_type = self._get_type_name(method_node.return_type) if method_node.return_type else "void"
            
            # 检查修饰符
            is_static = 'static' in method_node.modifiers if method_node.modifiers else False
            is_abstract = 'abstract' in method_node.modifiers if method_node.modifiers else False
            
            # 解析方法调用
            calls = self._extract_method_calls(method_node, class_name)
            
            # 获取行号
            line_number = 0
            if hasattr(method_node, 'position') and method_node.position:
                if hasattr(method_node.position, 'line'):
                    line_number = method_node.position.line
                elif isinstance(method_node.position, dict):
                    line_number = method_node.position.get('line', 0)

            return MethodInfo(
                class_name=class_name,
                method_name=method_name,
                parameters=parameters,
                return_type=return_type,
                is_static=is_static,
                is_abstract=is_abstract,
                file_path=file_path,
                line_number=line_number,
                calls=calls
            )
        except Exception as e:
            print(f"解析方法失败 {class_name}.{method_node.name}: {e}")
            return None
    
    def _extract_method_calls(self, method_node, caller_class: str) -> List[MethodCall]:
        """提取方法中的所有方法调用"""
        calls = []

        if not method_node.body:
            return calls

        try:
            # 创建一个临时的AST节点来使用filter
            temp_tree = javalang.tree.CompilationUnit()
            temp_tree.types = [javalang.tree.ClassDeclaration(
                name="TempClass",
                body=[method_node]
            )]

            # 使用tree.filter查找方法调用
            for path, node in temp_tree.filter(javalang.tree.MethodInvocation):
                try:
                    call_info = self._parse_method_invocation(node, caller_class, method_node.name)
                    if call_info:
                        calls.append(call_info)
                except Exception as e:
                    print(f"解析方法调用失败: {e}")
                    continue
        except Exception as e:
            print(f"提取方法调用时出错: {e}")
            # 备用方案：递归遍历
            calls.extend(self._recursive_extract_calls(method_node.body, caller_class, method_node.name))

        return calls

    def _recursive_extract_calls(self, node, caller_class: str, caller_method: str) -> List[MethodCall]:
        """递归提取方法调用"""
        calls = []

        if isinstance(node, javalang.tree.MethodInvocation):
            call_info = self._parse_method_invocation(node, caller_class, caller_method)
            if call_info:
                calls.append(call_info)

        # 递归遍历子节点
        if hasattr(node, 'children'):
            for child in node.children:
                if child:
                    if isinstance(child, list):
                        for item in child:
                            calls.extend(self._recursive_extract_calls(item, caller_class, caller_method))
                    else:
                        calls.extend(self._recursive_extract_calls(child, caller_class, caller_method))

        return calls

    def _extract_calls_from_statement(self, stmt, caller_class: str, caller_method: str) -> List[MethodCall]:
        """从语句中提取方法调用"""
        calls = []

        try:
            # 递归遍历语句树
            if hasattr(stmt, 'filter'):
                for path, node in stmt.filter(javalang.tree.MethodInvocation):
                    call_info = self._parse_method_invocation(node, caller_class, caller_method)
                    if call_info:
                        calls.append(call_info)
        except:
            pass

        return calls

    def _parse_method_invocation(self, node, caller_class: str, caller_method: str) -> Optional[MethodCall]:
        """解析方法调用节点"""
        try:
            called_method = node.member
            
            # 获取调用的类名
            called_class = self._resolve_called_class(node, caller_class)
            
            # 获取参数类型
            parameters = []
            if node.arguments:
                for arg in node.arguments:
                    param_type = self._infer_argument_type(arg)
                    parameters.append(param_type)
            
            # 获取行号
            line_number = 0
            if hasattr(node, 'position') and node.position:
                if hasattr(node.position, 'line'):
                    line_number = node.position.line
                elif isinstance(node.position, dict):
                    line_number = node.position.get('line', 0)

            return MethodCall(
                caller_class=caller_class,
                caller_method=caller_method,
                called_class=called_class,
                called_method=called_method,
                parameters=parameters,
                line_number=line_number,
                call_type='direct'  # 后续会根据继承关系调整
            )
        except Exception as e:
            print(f"解析方法调用节点失败: {e}")
            return None
    
    def _resolve_called_class(self, node, caller_class: str) -> str:
        """解析被调用方法的类名"""
        if hasattr(node, 'qualifier') and node.qualifier:
            if hasattr(node.qualifier, 'member'):
                # 链式调用，如 obj.method().anotherMethod()
                return "Unknown"  # 需要更复杂的类型推断
            elif hasattr(node.qualifier, 'value'):
                # 静态调用，如 ClassName.method()
                return node.qualifier.value
            else:
                return "Unknown"
        else:
            # 当前类的方法调用
            return caller_class
    
    def _get_type_name(self, type_node) -> str:
        """获取类型名称"""
        if not type_node:
            return "void"
        
        if hasattr(type_node, 'name'):
            return type_node.name
        elif hasattr(type_node, 'type'):
            return self._get_type_name(type_node.type)
        else:
            return str(type_node)
    
    def _infer_argument_type(self, arg_node) -> str:
        """推断参数类型"""
        # 简化的类型推断
        if hasattr(arg_node, 'value'):
            if isinstance(arg_node.value, str):
                return "String"
            elif isinstance(arg_node.value, (int, float)):
                return "Number"
        
        return "Object"
    
    def _is_abstract(self, node) -> bool:
        """检查是否为抽象类"""
        return 'abstract' in node.modifiers if hasattr(node, 'modifiers') and node.modifiers else False
    
    def get_method_by_signature(self, file_path: str, method_name: str) -> Optional[MethodInfo]:
        """根据文件路径和方法名获取方法信息"""
        # 标准化文件路径
        normalized_path = file_path.replace('\\', '/').replace('.java', '')
        
        # 查找匹配的方法
        for method_key, method_info in self.methods.items():
            if method_info.method_name == method_name:
                method_file_path = method_info.file_path.replace('\\', '/').replace('.java', '')
                if normalized_path in method_file_path or method_file_path.endswith(normalized_path):
                    return method_info
        
        return None
    
    def is_project_method(self, class_name: str) -> bool:
        """判断是否为项目内的方法"""
        if not class_name or class_name == "Unknown":
            return False
        
        # 检查是否为排除的包
        for excluded_package in EXCLUDED_PACKAGES:
            if class_name.startswith(excluded_package):
                return False
        
        # 检查是否在项目的类列表中
        return class_name in self.package_mapping or any(
            class_name in full_name for full_name in self.classes.keys()
        )
