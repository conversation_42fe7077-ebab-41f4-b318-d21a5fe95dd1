<template>
  <div class="analysis-container">
    <div class="content-wrapper">
      <!-- 进度指示器 -->
      <div class="progress-section">
        <el-card class="progress-card card-shadow">
          <template #header>
            <div class="card-header">
              <el-icon><Search /></el-icon>
              <span>方法选择与分析</span>
            </div>
          </template>
          
          <el-steps :active="currentStep" finish-status="success" class="analysis-steps">
            <el-step title="项目解析" description="解析Java项目结构" />
            <el-step title="选择方法" description="选择要分析的目标方法" />
            <el-step title="调用链分析" description="分析方法调用关系" />
            <el-step title="结果展示" description="查看分析结果" />
          </el-steps>
        </el-card>
      </div>
      
      <!-- 项目信息 -->
      <div v-if="taskData" class="project-info">
        <el-card class="info-card card-shadow">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              <span>项目信息</span>
            </div>
          </template>
          
          <div class="project-stats">
            <div class="stat-item">
              <el-icon><Files /></el-icon>
              <span class="stat-label">类总数</span>
              <span class="stat-value">{{ taskData.result?.statistics?.total_classes || 0 }}</span>
            </div>
            <div class="stat-item">
              <el-icon><Operation /></el-icon>
              <span class="stat-label">方法总数</span>
              <span class="stat-value">{{ taskData.result?.statistics?.total_methods || 0 }}</span>
            </div>
            <div class="stat-item">
              <el-icon><Connection /></el-icon>
              <span class="stat-label">接口数</span>
              <span class="stat-value">{{ taskData.result?.statistics?.interfaces || 0 }}</span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 方法选择区域 -->
      <div v-if="showMethodSelection" class="method-selection">
        <el-card class="selection-card card-shadow">
          <template #header>
            <div class="card-header">
              <el-icon><Search /></el-icon>
              <span>选择要分析的方法</span>
            </div>
          </template>
          
          <!-- 搜索和过滤 -->
          <div class="search-filters">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索类名或方法名..."
                  :prefix-icon="Search"
                  clearable
                />
              </el-col>
              <el-col :span="12">
                <el-select v-model="classFilter" placeholder="选择类型" clearable>
                  <el-option label="所有类" value="" />
                  <el-option label="Service类" value="service" />
                  <el-option label="Controller类" value="controller" />
                  <el-option label="Repository类" value="repository" />
                </el-select>
              </el-col>
            </el-row>
          </div>
          
          <!-- 类和方法列表 -->
          <div class="classes-list">
            <div v-for="(classInfo, className) in filteredClasses" :key="className" class="class-group">
              <div class="class-header">
                <el-icon><Document /></el-icon>
                <span class="class-name">{{ getSimpleClassName(className) }}</span>
                <el-tag size="small" type="info">{{ classInfo.methods.length }} 个方法</el-tag>
              </div>
              
              <div class="methods-list">
                <div
                  v-for="method in classInfo.methods"
                  :key="`${className}.${method.name}`"
                  class="method-item"
                  :class="{ selected: selectedMethod?.key === `${className}.${method.name}` }"
                  @click="selectMethod(className, classInfo.file_path, method)"
                >
                  <div class="method-info">
                    <span class="method-name">{{ method.name }}</span>
                    <span class="method-params">({{ method.parameters.join(', ') || 'void' }})</span>
                    <el-tag size="small" class="return-type">{{ method.return_type }}</el-tag>
                  </div>
                  <el-icon class="select-icon"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分析按钮 -->
          <div class="analysis-actions">
            <el-button
              type="primary"
              size="large"
              class="gradient-button"
              :disabled="!selectedMethod"
              :loading="analyzing"
              @click="startAnalysis"
            >
              <el-icon><VideoPlay /></el-icon>
              {{ analyzing ? '分析中...' : '开始分析' }}
            </el-button>
          </div>
        </el-card>
      </div>
      
      <!-- 分析进度 -->
      <div v-if="analyzing" class="analysis-progress">
        <el-card class="progress-card card-shadow">
          <template #header>
            <div class="card-header">
              <el-icon><Loading /></el-icon>
              <span>正在分析调用链</span>
            </div>
          </template>
          
          <div class="progress-content">
            <el-progress
              :percentage="analysisProgress"
              :status="analysisStatus"
              :stroke-width="12"
              class="analysis-progress-bar"
            />
            <p class="progress-text">{{ progressMessage }}</p>
            
            <div class="progress-details">
              <div class="detail-item">
                <el-icon><Clock /></el-icon>
                <span>预计剩余时间: {{ estimatedTime }}</span>
              </div>
              <div class="detail-item">
                <el-icon><Document /></el-icon>
                <span>当前步骤: {{ currentAnalysisStep }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { ElMessage } from 'element-plus'
import {
  Search,
  Document,
  Files,
  Operation,
  Connection,
  ArrowRight,
  VideoPlay,
  Loading,
  Clock
} from '@element-plus/icons-vue'
import socketManager from '@/utils/socket'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()

// Props
const props = defineProps({
  taskId: {
    type: String,
    required: true
  }
})

// 响应式数据
const taskData = ref(null)
const currentStep = ref(1)
const searchKeyword = ref('')
const classFilter = ref('')
const selectedMethod = ref(null)
const analyzing = ref(false)
const analysisProgress = ref(0)
const analysisStatus = ref('')
const progressMessage = ref('')
const estimatedTime = ref('计算中...')
const currentAnalysisStep = ref('')

// 计算属性
const showMethodSelection = computed(() => {
  return taskData.value?.status === 'completed' && !analyzing.value
})

const filteredClasses = computed(() => {
  if (!taskData.value?.result?.classes) return {}
  
  const classes = taskData.value.result.classes
  const filtered = {}
  
  Object.entries(classes).forEach(([className, classInfo]) => {
    // 类型过滤
    if (classFilter.value) {
      const lowerClassName = className.toLowerCase()
      if (!lowerClassName.includes(classFilter.value.toLowerCase())) {
        return
      }
    }
    
    // 关键词搜索
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase()
      const matchClass = className.toLowerCase().includes(keyword)
      const matchMethods = classInfo.methods.some(method => 
        method.name.toLowerCase().includes(keyword)
      )
      
      if (!matchClass && !matchMethods) {
        return
      }
    }
    
    filtered[className] = classInfo
  })
  
  return filtered
})

// 方法
const getSimpleClassName = (fullClassName) => {
  return fullClassName.split('.').pop()
}

const selectMethod = (className, filePath, method) => {
  selectedMethod.value = {
    key: `${className}.${method.name}`,
    className,
    filePath,
    method
  }
  
  ElMessage.success(`已选择方法: ${getSimpleClassName(className)}.${method.name}`)
}

const startAnalysis = async () => {
  if (!selectedMethod.value) {
    ElMessage.warning('请先选择要分析的方法')
    return
  }
  
  try {
    analyzing.value = true
    analysisProgress.value = 0
    currentStep.value = 2
    
    // 启动分析
    const result = await appStore.startAnalysis(
      props.taskId,
      selectedMethod.value.filePath,
      selectedMethod.value.method.name
    )
    
    ElMessage.success('分析任务已启动')
    
  } catch (error) {
    analyzing.value = false
    ElMessage.error(error.message)
  }
}

const loadTaskData = async () => {
  try {
    const data = await appStore.getTaskStatus(props.taskId)
    taskData.value = data
    
    if (data.status === 'completed') {
      currentStep.value = 1
    } else if (data.status === 'failed') {
      ElMessage.error(data.error || '项目解析失败')
      router.push('/')
    }
  } catch (error) {
    ElMessage.error('获取项目信息失败')
    router.push('/')
  }
}

// WebSocket事件处理
const handleAnalysisUpdate = (data) => {
  analysisProgress.value = data.progress || 0
  progressMessage.value = data.message || ''
  currentAnalysisStep.value = data.message || ''
  
  // 估算剩余时间
  if (data.progress > 0) {
    const remaining = (100 - data.progress) / data.progress * 2 // 简单估算
    estimatedTime.value = remaining > 1 ? `${Math.ceil(remaining)} 分钟` : '不到1分钟'
  }
}

const handleAnalysisComplete = (data) => {
  analyzing.value = false
  analysisProgress.value = 100
  analysisStatus.value = 'success'
  progressMessage.value = '分析完成！'
  currentStep.value = 3
  
  ElMessage.success('分析完成！正在跳转到结果页面...')
  
  setTimeout(() => {
    router.push(`/results/${data.analysis_id}`)
  }, 2000)
}

const handleAnalysisError = (data) => {
  analyzing.value = false
  analysisStatus.value = 'exception'
  progressMessage.value = data.error || '分析失败'
  
  ElMessage.error(data.error || '分析失败')
}

// 生命周期
onMounted(() => {
  loadTaskData()
  
  // 监听WebSocket事件
  socketManager.on('analysis_update', handleAnalysisUpdate)
  socketManager.on('analysis_complete', handleAnalysisComplete)
  socketManager.on('analysis_error', handleAnalysisError)
})

onUnmounted(() => {
  // 清理WebSocket监听器
  socketManager.off('analysis_update', handleAnalysisUpdate)
  socketManager.off('analysis_complete', handleAnalysisComplete)
  socketManager.off('analysis_error', handleAnalysisError)
})
</script>

<style scoped>
.analysis-container {
  min-height: 100vh;
  padding: 20px;
  background: transparent;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 20px;
}

.progress-card,
.info-card,
.selection-card {
  border: none;
  border-radius: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.analysis-steps {
  margin-top: 20px;
}

.project-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9ff;
  border-radius: 12px;
}

.stat-label {
  flex: 1;
  color: #666;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #667eea;
}

.search-filters {
  margin-bottom: 24px;
}

.classes-list {
  max-height: 600px;
  overflow-y: auto;
}

.class-group {
  margin-bottom: 24px;
}

.class-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 12px;
}

.class-name {
  flex: 1;
  font-weight: 600;
  color: #333;
}

.methods-list {
  display: grid;
  gap: 8px;
}

.method-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.method-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.method-item.selected {
  border-color: #67c23a;
  background-color: #f0f9ff;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.method-name {
  font-weight: 600;
  color: #333;
}

.method-params {
  color: #666;
  font-family: 'Courier New', monospace;
}

.return-type {
  background: #667eea;
  color: white;
}

.select-icon {
  color: #667eea;
  transition: transform 0.3s ease;
}

.method-item:hover .select-icon {
  transform: translateX(4px);
}

.analysis-actions {
  text-align: center;
  margin-top: 32px;
}

.progress-content {
  text-align: center;
}

.analysis-progress-bar {
  margin-bottom: 20px;
}

.progress-text {
  font-size: 16px;
  color: #666;
  margin: 16px 0;
}

.progress-details {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-top: 24px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-container {
    padding: 10px;
  }
  
  .project-stats {
    grid-template-columns: 1fr;
  }
  
  .progress-details {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
