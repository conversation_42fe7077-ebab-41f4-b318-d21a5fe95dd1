<template>
  <div class="results-container">
    <div class="content-wrapper">
      <!-- 结果头部 -->
      <div class="results-header">
        <el-card class="header-card card-shadow">
          <div class="header-content">
            <div class="header-info">
              <h2>分析结果</h2>
              <p v-if="resultData">{{ resultData.target_method }} 的调用链分析</p>
            </div>
            <div class="header-actions">
              <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
              <el-button type="primary" @click="analyzeNew" :icon="Plus">分析新项目</el-button>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 统计信息 -->
      <div v-if="resultData" class="statistics-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card card-shadow">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ resultData.statistics.total_paths }}</div>
                  <div class="stat-label">调用路径</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card card-shadow">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon><Sort /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ resultData.statistics.max_depth }}</div>
                  <div class="stat-label">最大深度</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card card-shadow">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon><Operation /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ resultData.statistics.unique_methods }}</div>
                  <div class="stat-label">涉及方法</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card card-shadow">
              <div class="stat-content">
                <div class="stat-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-number" :class="{ warning: resultData.statistics.cycles_detected > 0 }">
                    {{ resultData.statistics.cycles_detected }}
                  </div>
                  <div class="stat-label">循环检测</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 主要内容 -->
      <div v-if="resultData" class="main-content">
        <el-tabs v-model="activeTab" class="result-tabs">
          <!-- 调用路径 -->
          <el-tab-pane label="调用路径" name="paths">
            <template #label>
              <span><el-icon><Share /></el-icon> 调用路径</span>
            </template>
            
            <div class="paths-content">
              <div v-if="resultData.statistics.cycles_detected > 0" class="cycle-warning">
                <el-alert
                  title="检测到循环调用"
                  type="warning"
                  description="发现可能的递归调用，请注意潜在的性能风险"
                  :closable="false"
                  show-icon
                />
              </div>
              
              <div class="paths-list">
                <div
                  v-for="(path, index) in resultData.call_paths"
                  :key="index"
                  class="path-item"
                >
                  <div class="path-header">
                    <span class="path-title">路径 {{ index + 1 }}</span>
                    <el-tag size="small" type="info">深度: {{ path.depth }}</el-tag>
                  </div>
                  
                  <div class="path-visualization">
                    <div
                      v-for="(method, methodIndex) in path.path"
                      :key="methodIndex"
                      class="path-step"
                    >
                      <div v-if="method === 'CYCLE_DETECTED'" class="cycle-indicator">
                        <el-tag type="warning" size="small">
                          <el-icon><Refresh /></el-icon>
                          循环检测
                        </el-tag>
                      </div>
                      <div v-else class="method-node">
                        <span class="method-name">{{ formatMethodName(method) }}</span>
                      </div>
                      
                      <div
                        v-if="methodIndex < path.path.length - 1 && path.path[methodIndex + 1] !== 'CYCLE_DETECTED'"
                        class="path-arrow"
                      >
                        <el-icon><ArrowRight /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 可视化图表 -->
          <el-tab-pane label="可视化图表" name="chart">
            <template #label>
              <span><el-icon><PieChart /></el-icon> 可视化图表</span>
            </template>
            
            <div class="chart-content">
              <div class="chart-placeholder">
                <el-icon class="chart-icon"><PieChart /></el-icon>
                <h3>调用关系图表</h3>
                <p>此功能需要后端生成的流程图文件</p>
                <el-button type="primary" @click="downloadFlowchart">
                  <el-icon><Download /></el-icon>
                  下载流程图
                </el-button>
              </div>
            </div>
          </el-tab-pane>
          
          <!-- 详细报告 -->
          <el-tab-pane label="详细报告" name="report">
            <template #label>
              <span><el-icon><Document /></el-icon> 详细报告</span>
            </template>
            
            <div class="report-content">
              <div class="report-actions">
                <el-button type="primary" @click="downloadReport">
                  <el-icon><Download /></el-icon>
                  下载报告
                </el-button>
                <el-button @click="copyReport">
                  <el-icon><DocumentCopy /></el-icon>
                  复制内容
                </el-button>
              </div>
              
              <div class="report-text">
                <pre v-if="reportContent">{{ reportContent }}</pre>
                <div v-else class="loading-report">
                  <el-icon class="loading-icon"><Loading /></el-icon>
                  <span>正在加载报告...</span>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <!-- 加载状态 -->
      <div v-else class="loading-container">
        <el-card class="loading-card card-shadow">
          <div class="loading-content">
            <el-icon class="loading-spinner"><Loading /></el-icon>
            <h3>正在加载分析结果...</h3>
            <p>请稍候，正在获取详细的分析数据</p>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Plus,
  Connection,
  Sort,
  Operation,
  Warning,
  Share,
  ArrowRight,
  Refresh,
  PieChart,
  Document,
  Download,
  DocumentCopy,
  Loading
} from '@element-plus/icons-vue'

const router = useRouter()
const appStore = useAppStore()

// Props
const props = defineProps({
  analysisId: {
    type: String,
    required: true
  }
})

// 响应式数据
const resultData = ref(null)
const activeTab = ref('paths')
const reportContent = ref('')

// 方法
const goBack = () => {
  router.go(-1)
}

const analyzeNew = () => {
  router.push('/')
}

const formatMethodName = (fullMethodName) => {
  if (!fullMethodName || typeof fullMethodName !== 'string') {
    return fullMethodName
  }
  
  // 提取类名和方法名的最后两部分
  const parts = fullMethodName.split('.')
  if (parts.length >= 2) {
    return parts.slice(-2).join('.')
  }
  return fullMethodName
}

const downloadFlowchart = async () => {
  try {
    const url = `/api/download/${props.analysisId}/flowchart`
    const link = document.createElement('a')
    link.href = url
    link.download = `flowchart_${props.analysisId}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('流程图下载已开始')
  } catch (error) {
    ElMessage.error('下载流程图失败')
  }
}

const downloadReport = async () => {
  try {
    const url = `/api/download/${props.analysisId}/report`
    const link = document.createElement('a')
    link.href = url
    link.download = `report_${props.analysisId}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('报告下载已开始')
  } catch (error) {
    ElMessage.error('下载报告失败')
  }
}

const copyReport = async () => {
  if (!reportContent.value) {
    ElMessage.warning('报告内容为空')
    return
  }
  
  try {
    await navigator.clipboard.writeText(reportContent.value)
    ElMessage.success('报告内容已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const loadResults = async () => {
  try {
    const data = await appStore.getAnalysisResult(props.analysisId)
    resultData.value = data
    
    // 加载报告内容
    loadReportContent()
    
  } catch (error) {
    ElMessage.error('获取分析结果失败')
    router.push('/')
  }
}

const loadReportContent = async () => {
  try {
    const response = await fetch(`/api/download/${props.analysisId}/report`)
    if (response.ok) {
      reportContent.value = await response.text()
    } else {
      reportContent.value = '报告加载失败'
    }
  } catch (error) {
    reportContent.value = '报告加载失败'
  }
}

// 生命周期
onMounted(() => {
  loadResults()
})
</script>

<style scoped>
.results-container {
  min-height: 100vh;
  padding: 20px;
  background: transparent;
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  gap: 24px;
}

.header-card {
  border: none;
  border-radius: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-info p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stat-card {
  border: none;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stat-number.warning {
  color: #f56c6c;
}

.stat-label {
  color: #666;
  font-size: 14px;
  margin-top: 4px;
}

.result-tabs {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cycle-warning {
  margin-bottom: 24px;
}

.paths-list {
  display: grid;
  gap: 20px;
}

.path-item {
  background: #f8f9ff;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #667eea;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.path-title {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.path-visualization {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.method-node {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #333;
}

.cycle-indicator {
  display: flex;
  align-items: center;
}

.path-arrow {
  color: #667eea;
  font-size: 16px;
}

.chart-content {
  text-align: center;
  padding: 60px 20px;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.chart-icon {
  font-size: 64px;
  color: #c0c4cc;
}

.chart-placeholder h3 {
  margin: 0;
  color: #333;
}

.chart-placeholder p {
  margin: 0;
  color: #666;
}

.report-content {
  padding: 20px 0;
}

.report-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.report-text {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.report-text pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.loading-report {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #666;
}

.loading-icon {
  font-size: 20px;
  animation: spin 1s linear infinite;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  border: none;
  border-radius: 16px;
  width: 100%;
  max-width: 400px;
}

.loading-content {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  font-size: 48px;
  color: #667eea;
  margin-bottom: 20px;
  animation: spin 1s linear infinite;
}

.loading-content h3 {
  margin: 0 0 12px 0;
  color: #333;
}

.loading-content p {
  margin: 0;
  color: #666;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .results-container {
    padding: 10px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .statistics-section .el-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .path-visualization {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .report-actions {
    flex-direction: column;
  }
}
</style>
