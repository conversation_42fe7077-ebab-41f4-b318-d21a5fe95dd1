<template>
  <div class="home-container">
    <div class="content-wrapper">
      <!-- 欢迎区域 -->
      <div class="welcome-section">
        <el-card class="welcome-card card-shadow">
          <div class="welcome-content">
            <div class="welcome-text">
              <h2>欢迎使用Java代码结构可视化工具</h2>
              <p>专业的Spring Boot项目分析平台，帮助您快速理解代码结构和方法调用关系</p>
              <div class="features">
                <div class="feature-item">
                  <el-icon><DocumentAdd /></el-icon>
                  <span>智能项目解析</span>
                </div>
                <div class="feature-item">
                  <el-icon><Connection /></el-icon>
                  <span>调用链分析</span>
                </div>
                <div class="feature-item">
                  <el-icon><PieChart /></el-icon>
                  <span>可视化展示</span>
                </div>
              </div>
            </div>
            <div class="welcome-image">
              <el-icon class="main-icon"><DataAnalysis /></el-icon>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 上传区域 -->
      <div class="upload-section">
        <el-card class="upload-card card-shadow">
          <template #header>
            <div class="card-header">
              <el-icon><Upload /></el-icon>
              <span>上传Spring Boot项目</span>
            </div>
          </template>
          
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :show-file-list="false"
            accept=".zip"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><UploadFilled /></el-icon>
              <div class="upload-text">
                <p class="upload-title">拖拽文件到此处或点击选择</p>
                <p class="upload-hint">支持ZIP格式的Spring Boot项目，最大1GB</p>
              </div>
            </div>
          </el-upload>
          
          <div v-if="selectedFile" class="file-info">
            <div class="file-details">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ selectedFile.name }}</span>
              <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
            </div>
            <el-button 
              type="primary" 
              class="gradient-button"
              :loading="uploading"
              @click="uploadFile"
              :disabled="!selectedFile"
            >
              <el-icon><Upload /></el-icon>
              {{ uploading ? '上传中...' : '开始上传' }}
            </el-button>
          </div>
          
          <!-- 上传进度 -->
          <div v-if="uploading" class="upload-progress">
            <el-progress 
              :percentage="uploadProgress" 
              :status="uploadStatus"
              :stroke-width="8"
            />
            <p class="progress-text">{{ progressText }}</p>
          </div>
        </el-card>
      </div>
      
      <!-- 使用说明 -->
      <div class="guide-section">
        <el-card class="guide-card card-shadow">
          <template #header>
            <div class="card-header">
              <el-icon><QuestionFilled /></el-icon>
              <span>使用指南</span>
            </div>
          </template>
          
          <el-steps :active="0" direction="vertical" class="guide-steps">
            <el-step title="上传项目" description="选择您的Spring Boot项目ZIP文件进行上传">
              <template #icon>
                <el-icon><Upload /></el-icon>
              </template>
            </el-step>
            <el-step title="选择方法" description="从解析的项目中选择要分析的目标方法">
              <template #icon>
                <el-icon><Search /></el-icon>
              </template>
            </el-step>
            <el-step title="查看结果" description="获得详细的调用链分析和可视化流程图">
              <template #icon>
                <el-icon><PieChart /></el-icon>
              </template>
            </el-step>
          </el-steps>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { ElMessage } from 'element-plus'
import {
  DocumentAdd,
  Connection,
  PieChart,
  DataAnalysis,
  Upload,
  UploadFilled,
  Document,
  QuestionFilled,
  Search
} from '@element-plus/icons-vue'
import socketManager from '@/utils/socket'

const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const uploadRef = ref()
const selectedFile = ref(null)
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const progressText = ref('')

// 计算属性
const loading = computed(() => appStore.loading)

// 方法
const handleFileChange = (file) => {
  selectedFile.value = file.raw
}

const beforeUpload = (file) => {
  const isZip = file.type === 'application/zip' || file.name.endsWith('.zip')
  const isLt1G = file.size / 1024 / 1024 / 1024 < 1

  if (!isZip) {
    ElMessage.error('只能上传ZIP格式的文件!')
    return false
  }
  if (!isLt1G) {
    ElMessage.error('文件大小不能超过1GB!')
    return false
  }
  return false // 阻止自动上传
}

const uploadFile = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  try {
    uploading.value = true
    uploadProgress.value = 0
    uploadStatus.value = ''
    progressText.value = '正在上传文件...'
    
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 200)
    
    // 监听WebSocket事件
    const handleTaskUpdate = (data) => {
      uploadProgress.value = data.progress || 100
      progressText.value = data.message || '处理中...'
      
      if (data.status === 'completed') {
        uploadStatus.value = 'success'
        progressText.value = '项目解析完成！'
        
        setTimeout(() => {
          router.push(`/analysis/${data.task_id}`)
        }, 1500)
      } else if (data.status === 'failed') {
        uploadStatus.value = 'exception'
        progressText.value = data.error || '解析失败'
      }
    }
    
    socketManager.on('task_update', handleTaskUpdate)
    
    // 上传文件
    const result = await appStore.uploadProject(selectedFile.value)
    
    clearInterval(progressInterval)
    uploadProgress.value = 100
    progressText.value = '上传完成，正在解析项目...'
    
    // 加入任务房间以接收实时更新
    socketManager.joinTask(result.task_id)
    
  } catch (error) {
    uploading.value = false
    uploadStatus.value = 'exception'
    uploadProgress.value = 0
    ElMessage.error(error.message)
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  padding: 40px 20px;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 30px;
  grid-template-columns: 1fr;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-card {
  border: none;
  border-radius: 16px;
  overflow: hidden;
}

.welcome-content {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 20px;
}

.welcome-text {
  flex: 1;
}

.welcome-text h2 {
  margin: 0 0 16px 0;
  font-size: 32px;
  font-weight: 600;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-text p {
  margin: 0 0 24px 0;
  font-size: 18px;
  color: #666;
  line-height: 1.6;
}

.features {
  display: flex;
  gap: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #f8f9ff;
  border-radius: 20px;
  color: #667eea;
  font-weight: 500;
}

.welcome-image {
  flex-shrink: 0;
}

.main-icon {
  font-size: 120px;
  color: #667eea;
  opacity: 0.8;
}

.upload-card,
.guide-card {
  border: none;
  border-radius: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.upload-dragger {
  width: 100%;
}

.upload-content {
  padding: 40px;
  text-align: center;
}

.upload-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.upload-hint {
  margin: 0;
  font-size: 14px;
  color: #999;
}

.file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px;
  background: #f8f9ff;
  border-radius: 8px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-name {
  font-weight: 500;
  color: #333;
}

.file-size {
  color: #999;
  font-size: 14px;
}

.upload-progress {
  margin-top: 20px;
}

.progress-text {
  margin: 12px 0 0 0;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.guide-steps {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-container {
    padding: 20px 10px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .welcome-text h2 {
    font-size: 24px;
  }
  
  .features {
    flex-direction: column;
    align-items: center;
  }
  
  .main-icon {
    font-size: 80px;
  }
  
  .file-info {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
