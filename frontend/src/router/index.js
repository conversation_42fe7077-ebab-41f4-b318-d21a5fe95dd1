import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Analysis from '@/views/Analysis.vue'
import Results from '@/views/Results.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/analysis/:taskId',
    name: 'Analysis',
    component: Analysis,
    props: true,
    meta: {
      title: '方法分析'
    }
  },
  {
    path: '/results/:analysisId',
    name: 'Results',
    component: Results,
    props: true,
    meta: {
      title: '分析结果'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title} - Java代码结构可视化工具`
  next()
})

export default router
