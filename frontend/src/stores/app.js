import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useAppStore = defineStore('app', () => {
  // 状态
  const currentTask = ref(null)
  const currentAnalysis = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 计算属性
  const hasCurrentTask = computed(() => !!currentTask.value)
  const hasCurrentAnalysis = computed(() => !!currentAnalysis.value)
  
  // 操作
  const setCurrentTask = (task) => {
    currentTask.value = task
  }
  
  const setCurrentAnalysis = (analysis) => {
    currentAnalysis.value = analysis
  }
  
  const setLoading = (state) => {
    loading.value = state
  }
  
  const setError = (errorMessage) => {
    error.value = errorMessage
  }
  
  const clearError = () => {
    error.value = null
  }
  
  const reset = () => {
    currentTask.value = null
    currentAnalysis.value = null
    loading.value = false
    error.value = null
  }
  
  // API调用
  const uploadProject = async (file) => {
    try {
      setLoading(true)
      setError(null)
      
      const formData = new FormData()
      formData.append('project_file', file)
      
      const response = await api.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      setCurrentTask(response.data)
      return response.data
    } catch (error) {
      const errorMessage = error.response?.data?.error || '上传失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }
  
  const getTaskStatus = async (taskId) => {
    try {
      const response = await api.get(`/api/tasks/${taskId}`)
      setCurrentTask(response.data)
      return response.data
    } catch (error) {
      const errorMessage = error.response?.data?.error || '获取任务状态失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }
  
  const startAnalysis = async (taskId, targetFile, targetMethod) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await api.post('/api/analyze', {
        task_id: taskId,
        target_file: targetFile,
        target_method: targetMethod
      })
      
      return response.data
    } catch (error) {
      const errorMessage = error.response?.data?.error || '启动分析失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }
  
  const getAnalysisResult = async (analysisId) => {
    try {
      const response = await api.get(`/api/results/${analysisId}`)
      setCurrentAnalysis(response.data)
      return response.data
    } catch (error) {
      const errorMessage = error.response?.data?.error || '获取分析结果失败'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }
  
  return {
    // 状态
    currentTask,
    currentAnalysis,
    loading,
    error,
    
    // 计算属性
    hasCurrentTask,
    hasCurrentAnalysis,
    
    // 操作
    setCurrentTask,
    setCurrentAnalysis,
    setLoading,
    setError,
    clearError,
    reset,
    
    // API调用
    uploadProject,
    getTaskStatus,
    startAnalysis,
    getAnalysisResult
  }
})
