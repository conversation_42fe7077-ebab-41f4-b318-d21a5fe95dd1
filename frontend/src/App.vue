<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 头部 -->
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo">
            <el-icon class="logo-icon"><Connection /></el-icon>
            <h1>Java代码结构可视化工具</h1>
          </div>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="goHome"
              :icon="House"
            >
              首页
            </el-button>
          </div>
        </div>
      </el-header>
      
      <!-- 主要内容 -->
      <el-main class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
      
      <!-- 底部 -->
      <el-footer class="app-footer">
        <div class="footer-content">
          <p>&copy; 2024 Java代码结构可视化工具 - 专业的Spring Boot项目分析平台</p>
          <div class="footer-links">
            <el-link href="#" type="primary">使用指南</el-link>
            <el-link href="#" type="primary">API文档</el-link>
            <el-link href="#" type="primary">技术支持</el-link>
          </div>
        </div>
      </el-footer>
    </el-container>
    
    <!-- 全局错误提示 -->
    <el-dialog
      v-model="showError"
      title="错误提示"
      width="400px"
      :show-close="false"
    >
      <div class="error-content">
        <el-icon class="error-icon"><WarningFilled /></el-icon>
        <p>{{ errorMessage }}</p>
      </div>
      <template #footer>
        <el-button type="primary" @click="clearError">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { Connection, House, WarningFilled } from '@element-plus/icons-vue'
import socketManager from '@/utils/socket'

const router = useRouter()
const appStore = useAppStore()

// 计算属性
const showError = computed(() => !!appStore.error)
const errorMessage = computed(() => appStore.error)

// 方法
const goHome = () => {
  router.push('/')
}

const clearError = () => {
  appStore.clearError()
}

// 生命周期
onMounted(() => {
  // 连接WebSocket
  socketManager.connect()
})

onUnmounted(() => {
  // 断开WebSocket连接
  socketManager.disconnect()
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: transparent;
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  color: #667eea;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-main {
  padding: 0;
  background: transparent;
  min-height: calc(100vh - 120px);
}

.app-footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  height: 60px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.error-icon {
  font-size: 24px;
  color: #f56c6c;
}

.error-content p {
  margin: 0;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content,
  .footer-content {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }
  
  .logo h1 {
    font-size: 20px;
  }
  
  .footer-links {
    gap: 10px;
  }
}
</style>
