import { io } from 'socket.io-client'
import { ElMessage } from 'element-plus'

class SocketManager {
  constructor() {
    this.socket = null
    this.connected = false
    this.listeners = new Map()
  }
  
  connect(url = 'http://localhost:5002') {
    if (this.socket) {
      this.disconnect()
    }
    
    this.socket = io(url, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    })
    
    this.socket.on('connect', () => {
      this.connected = true
      console.log('WebSocket连接成功')
    })
    
    this.socket.on('disconnect', () => {
      this.connected = false
      console.log('WebSocket连接断开')
    })
    
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      ElMessage.error('实时连接失败，部分功能可能受影响')
    })
    
    return this.socket
  }
  
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.connected = false
    }
  }
  
  on(event, callback) {
    if (this.socket) {
      this.socket.on(event, callback)
      
      // 保存监听器以便后续清理
      if (!this.listeners.has(event)) {
        this.listeners.set(event, [])
      }
      this.listeners.get(event).push(callback)
    }
  }
  
  off(event, callback) {
    if (this.socket) {
      this.socket.off(event, callback)
      
      // 从监听器列表中移除
      if (this.listeners.has(event)) {
        const callbacks = this.listeners.get(event)
        const index = callbacks.indexOf(callback)
        if (index > -1) {
          callbacks.splice(index, 1)
        }
      }
    }
  }
  
  emit(event, data) {
    if (this.socket && this.connected) {
      this.socket.emit(event, data)
    }
  }
  
  joinTask(taskId) {
    this.emit('join_task', { task_id: taskId })
  }
  
  // 清理所有监听器
  clearListeners() {
    if (this.socket) {
      for (const [event, callbacks] of this.listeners) {
        callbacks.forEach(callback => {
          this.socket.off(event, callback)
        })
      }
      this.listeners.clear()
    }
  }
}

// 创建单例实例
const socketManager = new SocketManager()

export default socketManager
