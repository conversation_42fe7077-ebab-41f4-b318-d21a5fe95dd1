# Java Code Visualizer

一个专门用于分析Java项目（特别是Spring Boot项目）方法调用链的可视化工具，提供Web界面和命令行两种使用方式。

## 🚀 功能特点

- ✅ **完整的Java代码解析**: 使用AST解析Java源文件
- ✅ **智能调用链分析**: 处理继承、接口、重载和多态
- ✅ **业务逻辑专注**: 自动过滤JDK、Spring框架和外部库方法
- ✅ **可视化流程图**: 生成高质量的PNG/SVG流程图
- ✅ **循环检测**: 识别并标记递归调用
- ✅ **Spring Boot支持**: 专门优化Spring Boot项目分析
- ✅ **Web界面**: 现代化的Vue.js前端界面
- ✅ **实时分析**: WebSocket实时显示分析进度
- ✅ **多种输出格式**: 支持PNG、SVG、DOT格式

## 📁 项目结构

```
java_code_visualizer/
├── backend/                 # Python后端
│   ├── app.py              # Flask Web应用
│   ├── java_parser.py      # Java代码解析模块
│   ├── call_chain_analyzer.py # 调用链分析引擎
│   ├── filter.py           # 过滤和筛选逻辑
│   ├── flowchart_generator.py # 流程图生成器
│   ├── main.py             # 主程序逻辑
│   ├── cli.py              # 命令行界面
│   ├── run.py              # 启动脚本
│   └── requirements.txt    # Python依赖
├── frontend/               # Vue.js前端
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── utils/          # 工具函数
│   │   └── stores/         # 状态管理
│   ├── package.json        # Node.js依赖
│   └── vite.config.js      # Vite配置
├── start_all.py            # 一键启动脚本
├── start_backend.py        # 后端启动脚本
└── start_frontend.py       # 前端启动脚本
```

## 🛠️ 安装和运行

### 方式一：一键启动（推荐）

```bash
# 安装所有依赖并启动前后端
python start_all.py
```

### 方式二：分别启动

#### 启动后端
```bash
# 安装Python依赖
cd backend
pip install -r requirements.txt

# 启动后端服务
python app.py
```

#### 启动前端
```bash
# 安装Node.js依赖
cd frontend
npm install

# 启动前端开发服务器
npm run dev
```

### 方式三：使用启动脚本

```bash
# 启动后端
python start_backend.py

# 启动前端
python start_frontend.py
```

## 💻 使用方法

### Web界面使用

1. 启动服务后，访问 `http://localhost:5173`
2. 上传Java项目ZIP文件或选择本地项目目录
3. 输入要分析的Java文件路径和方法名
4. 点击"开始分析"，实时查看分析进度
5. 查看生成的调用链流程图和详细报告

### 命令行使用

```bash
cd backend

# 交互模式
python run.py

# 命令行模式
python run.py <项目路径> <目标文件> <目标方法> [输出路径]

# 示例
python run.py ./my-spring-project src/main/java/com/example/service/UserService.java getUserById
```

## 📋 系统要求

### Python环境
- Python 3.7+
- 依赖包：Flask, javalang, graphviz, networkx, matplotlib等

### Node.js环境
- Node.js 16+
- Vue.js 3, Vite等

### 系统工具
- **Graphviz**: 用于生成流程图
  - Windows: 下载安装 https://graphviz.org/download/
  - macOS: `brew install graphviz`
  - Linux: `sudo apt-get install graphviz`

## 🎯 使用示例

### 输入格式
- **项目路径**: Spring Boot项目的根目录或ZIP文件
- **目标文件**: Java文件的相对路径，如 `src/main/java/com/example/service/UserService.java`
- **目标方法**: 要分析的方法名，如 `getUserById`

### 输出文件
- `*.png`: 流程图图像文件
- `*_report.txt`: 详细的分析报告
- `*.dot`: Graphviz源文件（可选）

## 🔧 技术实现

### 后端技术栈
- **Flask**: Web框架
- **javalang**: Java代码AST解析
- **Graphviz**: 图形可视化
- **NetworkX**: 图算法处理
- **WebSocket**: 实时通信

### 前端技术栈
- **Vue.js 3**: 前端框架
- **Vite**: 构建工具
- **Pinia**: 状态管理
- **Vue Router**: 路由管理

### 核心算法
1. **JavaProjectParser**: 解析Java源文件，构建AST
2. **CallChainAnalyzer**: 分析方法调用关系，处理OOP特性
3. **CallFilter**: 智能过滤非业务逻辑方法
4. **FlowchartGenerator**: 生成可视化图表

## 📝 开发说明

详细的开发文档请参考 `backend/README.md`

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License
