#!/usr/bin/env python3
"""
后端API服务启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查后端依赖"""
    required_packages = ['flask', 'flask_cors', 'flask_socketio', 'javalang', 'graphviz', 'networkx']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r backend/requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("=== Java代码结构可视化工具 - 后端API服务 ===\n")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("✅ 依赖检查通过")
    
    # 创建必要的目录
    directories = ['backend/uploads', 'backend/results']
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")
    
    try:
        # 启动后端服务
        print("\n🚀 正在启动后端API服务...")
        print("📡 API地址: http://localhost:5001")
        print("📚 健康检查: http://localhost:5001/api/health")
        print("⏹️  按 Ctrl+C 停止服务器\n")
        
        # 切换到backend目录并启动应用
        os.chdir('backend')
        subprocess.run([sys.executable, 'app.py'])
        
    except KeyboardInterrupt:
        print("\n👋 后端服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
