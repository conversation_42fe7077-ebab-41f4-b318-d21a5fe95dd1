#!/usr/bin/env python3
"""
Java代码结构可视化工具 - 后端API服务
RESTful API服务，支持前后端分离架构
"""

import sys
import uuid
import json
import threading

from pathlib import Path
from datetime import datetime
from werkzeug.utils import secure_filename

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from backend.main import JavaCodeVisualizer
from backend.java_parser import JavaProjectParser

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'java_code_visualizer_api'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['RESULTS_FOLDER'] = 'results'
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 1GB

# 启用CORS跨域支持
CORS(app, origins=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"])

# 启用WebSocket支持
socketio = SocketIO(app, cors_allowed_origins=["http://localhost:3000", "http://localhost:8080"])

# 确保目录存在
Path(app.config['UPLOAD_FOLDER']).mkdir(exist_ok=True)
Path(app.config['RESULTS_FOLDER']).mkdir(exist_ok=True)

# 全局变量存储任务状态
analysis_tasks = {}


class AnalysisTask:
    """分析任务类"""
    def __init__(self, task_id: str):
        self.task_id = task_id
        self.status = 'pending'  # pending, parsing, analyzing, completed, failed
        self.progress = 0
        self.message = ''
        self.result = None
        self.error = None
        self.created_at = datetime.now()
        self.project_path = None
        self.classes = {}


@app.route('/', methods=['GET'])
def index():
    """根路径 - API信息页面"""
    return jsonify({
        'service': 'Java Code Visualizer API',
        'version': '1.0.0',
        'status': 'running',
        'endpoints': {
            'health': '/api/health',
            'upload': '/api/upload',
            'tasks': '/api/tasks/{task_id}',
            'analyze': '/api/analyze',
            'results': '/api/results/{analysis_id}',
            'download': '/api/download/{analysis_id}/{file_type}'
        },
        'websocket': 'ws://localhost:5001',
        'documentation': 'See API_DOCUMENTATION.md',
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'service': 'Java Code Visualizer API',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat()
    })


@app.route('/api/upload', methods=['POST'])
def upload_project():
    """
    上传项目文件接口
    ---
    POST /api/upload
    Content-Type: multipart/form-data
    
    Parameters:
    - project_file: ZIP文件
    
    Returns:
    {
        "task_id": "uuid",
        "message": "上传成功",
        "classes": {...},
        "statistics": {...}
    }
    """
    try:
        if 'project_file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['project_file']
        if file.filename == '':
            return jsonify({'error': '文件名为空'}), 400
        
        if not file.filename.endswith('.zip'):
            return jsonify({'error': '只支持ZIP格式文件'}), 400
        
        # 创建任务
        task_id = str(uuid.uuid4())
        task = AnalysisTask(task_id)
        analysis_tasks[task_id] = task
        
        # 保存上传文件
        filename = secure_filename(file.filename)
        upload_dir = Path(app.config['UPLOAD_FOLDER']) / task_id
        upload_dir.mkdir(exist_ok=True)
        
        file_path = upload_dir / filename
        file.save(str(file_path))
        
        # 解压文件
        extract_dir = upload_dir / 'project'
        extract_dir.mkdir(exist_ok=True)
        
        import zipfile
        with zipfile.ZipFile(str(file_path), 'r') as zip_ref:
            zip_ref.extractall(str(extract_dir))
        
        task.project_path = str(extract_dir)
        task.status = 'parsing'
        task.message = '正在解析项目结构...'
        
        # 在后台线程中解析项目
        thread = threading.Thread(target=parse_project_async, args=(task,))
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'status': 'parsing',
            'message': '文件上传成功，正在解析项目结构...'
        })
        
    except Exception as e:
        return jsonify({'error': f'上传失败: {str(e)}'}), 500


def parse_project_async(task: AnalysisTask):
    """异步解析项目"""
    try:
        task.progress = 20
        socketio.emit('task_update', {
            'task_id': task.task_id,
            'status': task.status,
            'progress': task.progress,
            'message': task.message
        })
        
        # 解析项目
        parser = JavaProjectParser()
        if parser.load_project(task.project_path):
            # 构建类信息
            classes_info = {}
            total_methods = 0
            
            for class_name, class_info in parser.classes.items():
                methods = []
                for method_key, method_info in class_info.methods.items():
                    methods.append({
                        'name': method_info.method_name,
                        'parameters': method_info.parameters,
                        'return_type': method_info.return_type,
                        'is_static': method_info.is_static,
                        'is_abstract': method_info.is_abstract,
                        'line_number': method_info.line_number
                    })
                    total_methods += 1
                
                if methods:  # 只包含有方法的类
                    classes_info[class_name] = {
                        'package': class_info.package,
                        'file_path': class_info.file_path,
                        'is_interface': class_info.is_interface,
                        'is_abstract': class_info.is_abstract,
                        'extends': class_info.extends,
                        'implements': class_info.implements,
                        'methods': methods
                    }
            
            task.classes = classes_info
            task.status = 'completed'
            task.progress = 100
            task.message = f'解析完成！找到 {len(classes_info)} 个类，{total_methods} 个方法'
            
            task.result = {
                'classes': classes_info,
                'statistics': {
                    'total_classes': len(classes_info),
                    'total_methods': total_methods,
                    'interfaces': sum(1 for c in classes_info.values() if c['is_interface']),
                    'abstract_classes': sum(1 for c in classes_info.values() if c['is_abstract'])
                }
            }
            
        else:
            raise Exception('项目解析失败，请确保上传的是有效的Java项目')
        
        socketio.emit('task_update', {
            'task_id': task.task_id,
            'status': task.status,
            'progress': task.progress,
            'message': task.message,
            'result': task.result
        })
        
    except Exception as e:
        task.status = 'failed'
        task.error = str(e)
        task.message = f'解析失败: {str(e)}'
        
        socketio.emit('task_update', {
            'task_id': task.task_id,
            'status': task.status,
            'error': task.error,
            'message': task.message
        })


@app.route('/api/tasks/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """
    获取任务状态接口
    ---
    GET /api/tasks/{task_id}
    
    Returns:
    {
        "task_id": "uuid",
        "status": "completed",
        "progress": 100,
        "message": "解析完成",
        "result": {...}
    }
    """
    task = analysis_tasks.get(task_id)
    if not task:
        return jsonify({'error': '任务不存在'}), 404
    
    response_data = {
        'task_id': task_id,
        'status': task.status,
        'progress': task.progress,
        'message': task.message,
        'created_at': task.created_at.isoformat()
    }
    
    if task.result:
        response_data['result'] = task.result
    
    if task.error:
        response_data['error'] = task.error
    
    return jsonify(response_data)


@app.route('/api/analyze', methods=['POST'])
def start_analysis():
    """
    开始分析接口
    ---
    POST /api/analyze
    Content-Type: application/json
    
    Body:
    {
        "task_id": "uuid",
        "target_file": "src/main/java/com/example/Service.java",
        "target_method": "methodName"
    }
    
    Returns:
    {
        "analysis_id": "uuid",
        "message": "分析已启动"
    }
    """
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        target_file = data.get('target_file')
        target_method = data.get('target_method')
        
        if not all([task_id, target_file, target_method]):
            return jsonify({'error': '缺少必要参数'}), 400
        
        task = analysis_tasks.get(task_id)
        if not task or task.status != 'completed':
            return jsonify({'error': '项目未解析完成或任务不存在'}), 400
        
        # 创建分析ID
        analysis_id = str(uuid.uuid4())
        
        # 在后台线程中执行分析
        thread = threading.Thread(
            target=run_analysis_async,
            args=(analysis_id, task, target_file, target_method)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'analysis_id': analysis_id,
            'message': '分析任务已启动'
        })
        
    except Exception as e:
        return jsonify({'error': f'启动分析失败: {str(e)}'}), 500


def run_analysis_async(analysis_id: str, task: AnalysisTask, target_file: str, target_method: str):
    """异步执行分析"""
    try:
        # 发送分析开始事件
        socketio.emit('analysis_update', {
            'analysis_id': analysis_id,
            'status': 'running',
            'progress': 10,
            'message': '正在初始化分析器...'
        })
        
        # 创建可视化工具
        visualizer = JavaCodeVisualizer()
        
        socketio.emit('analysis_update', {
            'analysis_id': analysis_id,
            'progress': 30,
            'message': '正在加载项目...'
        })
        
        # 加载项目
        if not visualizer.parser.load_project(task.project_path):
            raise Exception("项目加载失败")
        
        socketio.emit('analysis_update', {
            'analysis_id': analysis_id,
            'progress': 50,
            'message': '正在构建调用图...'
        })
        
        # 构建分析器
        from backend.call_chain_analyzer import CallChainAnalyzer
        from backend.filter import CallFilter, ProjectPackageDetector
        from backend.flowchart_generator import FlowchartGenerator
        
        project_packages = ProjectPackageDetector.detect_project_packages(visualizer.parser.classes)
        visualizer.filter = CallFilter(project_packages)
        visualizer.analyzer = CallChainAnalyzer(visualizer.parser)
        visualizer.generator = FlowchartGenerator(visualizer.analyzer)
        
        visualizer.analyzer.build_call_graph()
        
        socketio.emit('analysis_update', {
            'analysis_id': analysis_id,
            'progress': 70,
            'message': '正在查找目标方法...'
        })
        
        # 查找目标方法
        target_method_info = visualizer.parser.get_method_by_signature(target_file, target_method)
        if not target_method_info:
            raise Exception(f"未找到方法: {target_file}#{target_method}")
        
        socketio.emit('analysis_update', {
            'analysis_id': analysis_id,
            'progress': 85,
            'message': '正在分析调用链...'
        })
        
        # 提取调用链
        call_paths = visualizer.analyzer.extract_call_chain(target_file, target_method)
        
        socketio.emit('analysis_update', {
            'analysis_id': analysis_id,
            'progress': 95,
            'message': '正在生成结果...'
        })
        
        # 生成输出文件
        results_dir = Path(app.config['RESULTS_FOLDER']) / analysis_id
        results_dir.mkdir(parents=True, exist_ok=True)
        
        output_path = str(results_dir / 'flowchart')
        
        try:
            # 尝试生成流程图
            output_file = visualizer.generator.generate_flowchart(
                call_paths,
                output_path,
                f"{target_method_info.class_name}.{target_method_info.method_name} Call Chain"
            )
        except Exception as e:
            # 生成文本版本
            output_file = f"{output_path}_text.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"流程图生成失败: {e}\n")
                f.write("请安装Graphviz系统工具以生成图像文件\n")
        
        # 生成报告
        report = visualizer.generator.generate_summary_report(call_paths)
        report_file = str(results_dir / 'report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        # 构建结果数据
        result_data = {
            'analysis_id': analysis_id,
            'target_method': f"{target_method_info.class_name}.{target_method_info.method_name}",
            'call_paths': [
                {
                    'path': path.path,
                    'depth': path.depth,
                    'call_types': getattr(path, 'call_types', [])
                }
                for path in call_paths
            ],
            'statistics': {
                'total_paths': len(call_paths),
                'max_depth': max(path.depth for path in call_paths) if call_paths else 0,
                'unique_methods': len(set(method for path in call_paths for method in path.path if method != "CYCLE_DETECTED")),
                'cycles_detected': sum(1 for path in call_paths if "CYCLE_DETECTED" in path.path)
            },
            'files': {
                'flowchart': output_file.replace(str(results_dir), '').lstrip('/\\'),
                'report': 'report.txt'
            }
        }
        
        # 保存结果到文件
        result_file = str(results_dir / 'result.json')
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        # 发送完成事件
        socketio.emit('analysis_complete', {
            'analysis_id': analysis_id,
            'status': 'completed',
            'progress': 100,
            'message': '分析完成！',
            'result': result_data
        })
        
    except Exception as e:
        socketio.emit('analysis_error', {
            'analysis_id': analysis_id,
            'status': 'failed',
            'error': str(e),
            'message': f'分析失败: {str(e)}'
        })


@app.route('/api/results/<analysis_id>', methods=['GET'])
def get_analysis_result(analysis_id):
    """
    获取分析结果接口
    ---
    GET /api/results/{analysis_id}
    
    Returns:
    {
        "analysis_id": "uuid",
        "result": {...}
    }
    """
    try:
        result_file = Path(app.config['RESULTS_FOLDER']) / analysis_id / 'result.json'
        
        if not result_file.exists():
            return jsonify({'error': '分析结果不存在'}), 404
        
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        return jsonify(result_data)
        
    except Exception as e:
        return jsonify({'error': f'获取结果失败: {str(e)}'}), 500


@app.route('/api/download/<analysis_id>/<file_type>', methods=['GET'])
def download_file(analysis_id, file_type):
    """
    下载文件接口
    ---
    GET /api/download/{analysis_id}/{file_type}
    
    Parameters:
    - file_type: report | flowchart
    """
    try:
        results_dir = Path(app.config['RESULTS_FOLDER']) / analysis_id
        
        if file_type == 'report':
            file_path = results_dir / 'report.txt'
        elif file_type == 'flowchart':
            # 查找流程图文件
            for ext in ['.png', '.svg', '_text.txt']:
                file_path = results_dir / f'flowchart{ext}'
                if file_path.exists():
                    break
            else:
                return jsonify({'error': '流程图文件不存在'}), 404
        else:
            return jsonify({'error': '无效的文件类型'}), 400
        
        if file_path.exists():
            return send_file(str(file_path), as_attachment=True)
        else:
            return jsonify({'error': '文件不存在'}), 404
            
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500


# WebSocket事件处理
@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print(f'客户端已连接: {request.sid}')


@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    print(f'客户端已断开: {request.sid}')


@socketio.on('join_task')
def handle_join_task(data):
    """加入任务房间"""
    task_id = data.get('task_id')
    if task_id:
        join_room(task_id)
        print(f'客户端 {request.sid} 加入任务 {task_id}')


if __name__ == '__main__':
    print("=== Java代码结构可视化工具 - 后端API服务 ===")
    print("🚀 正在启动API服务器...")
    print("📡 API地址: http://localhost:5002")
    print("📚 API文档: http://localhost:5002/api/health")
    print("⏹️  按 Ctrl+C 停止服务器\n")
    
    try:
        socketio.run(app, debug=False, host='0.0.0.0', port=5002)
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
