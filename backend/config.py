"""
Configuration settings for Java Code Visualizer
"""

# 需要过滤的包前缀（JDK和常见框架）
EXCLUDED_PACKAGES = {
    'java.',
    'javax.',
    'org.springframework.',
    'org.apache.',
    'com.fasterxml.',
    'org.slf4j.',
    'org.junit.',
    'org.mockito.',
    'org.hibernate.',
    'org.mybatis.',
    'com.mysql.',
    'org.postgresql.',
    'redis.clients.',
    'org.mongodb.',
    'com.alibaba.fastjson.',
    'com.google.gson.',
    'org.json.',
    'lombok.',
}

# Spring Boot 特定的注解和方法
SPRING_ANNOTATIONS = {
    'Autowired',
    'Component',
    'Service',
    'Repository',
    'Controller',
    'RestController',
    'RequestMapping',
    'GetMapping',
    'PostMapping',
    'PutMapping',
    'DeleteMapping',
    'Transactional',
    'Configuration',
    'Bean',
    'Value',
    'Qualifier',
}

# 图形化配置
GRAPH_CONFIG = {
    'format': 'png',
    'engine': 'dot',
    'node_shape': 'box',
    'node_style': 'rounded,filled',
    'node_fillcolor': 'lightblue',
    'edge_color': 'black',
    'font_name': 'Arial',
    'font_size': '10',
    'dpi': '300',
}

# 递归深度限制
MAX_RECURSION_DEPTH = 10

# 支持的文件扩展名
SUPPORTED_EXTENSIONS = {'.java'}
