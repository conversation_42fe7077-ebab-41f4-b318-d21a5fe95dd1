# Java代码结构可视化工具

一个专门用于分析Spring Boot项目方法调用链的Python工具，能够生成清晰的流程图来可视化代码结构。

## 功能特点

- ✅ **完整的Java代码解析**: 使用AST解析Java源文件
- ✅ **智能调用链分析**: 处理继承、接口、重载和多态
- ✅ **业务逻辑专注**: 自动过滤JDK、Spring框架和外部库方法
- ✅ **可视化流程图**: 生成高质量的PNG/SVG流程图
- ✅ **循环检测**: 识别并标记递归调用
- ✅ **Spring Boot支持**: 专门优化Spring Boot项目分析
- ✅ **多种输出格式**: 支持PNG、SVG、DOT格式

## 安装依赖

```bash
pip install javalang graphviz networkx matplotlib
```

注意：还需要安装Graphviz系统工具：
- Windows: 下载并安装 https://graphviz.org/download/
- macOS: `brew install graphviz`
- Linux: `sudo apt-get install graphviz` 或 `sudo yum install graphviz`

## 使用方法

### 1. 交互模式（推荐）

```bash
python run.py
```

按照提示输入项目路径、目标文件和方法名。

### 2. 命令行模式

```bash
python run.py <项目路径> <目标文件> <目标方法> [输出路径]
```

示例：
```bash
python run.py ./my-spring-project src/main/java/com/example/service/UserService.java getUserById
```

### 3. 运行示例分析

```bash
python run.py --sample
```

### 4. 运行测试

```bash
python run.py --test
```

## 输入格式

- **项目路径**: Spring Boot项目的根目录或ZIP文件
- **目标文件**: Java文件的相对路径，例如 `src/main/java/com/example/service/UserService.java`
- **目标方法**: 要分析的方法名，例如 `getUserById`

## 输出文件

工具会生成以下文件：
- `*.png`: 流程图图像文件
- `*_report.txt`: 详细的分析报告
- `*.dot`: Graphviz源文件（可选）

## 项目结构

```
java_code_visualizer/
├── __init__.py              # 包初始化
├── config.py                # 配置文件
├── java_parser.py           # Java代码解析模块
├── call_chain_analyzer.py   # 调用链分析引擎
├── filter.py                # 过滤和筛选逻辑
├── flowchart_generator.py   # 流程图生成器
├── main.py                  # 主程序逻辑
├── cli.py                   # 命令行界面
├── run.py                   # 启动脚本
├── test_sample.py           # 测试用例
├── requirements.txt         # 依赖列表
└── README.md               # 说明文档
```

## 技术实现

### 核心组件

1. **JavaProjectParser**: 使用javalang库解析Java源文件，构建AST
2. **CallChainAnalyzer**: 分析方法调用关系，处理复杂的OOP特性
3. **CallFilter**: 智能过滤非业务逻辑方法
4. **FlowchartGenerator**: 使用Graphviz生成可视化图表

### 处理的复杂情况

- **继承关系**: 追踪父类和子类的方法调用
- **接口实现**: 识别接口方法的所有实现
- **方法重载**: 处理同名不同参数的方法
- **多态调用**: 显示所有可能的调用路径
- **循环检测**: 防止无限递归，标记循环调用

## 示例输出

分析`UserService.getUserById`方法后，工具会生成：

```
UserService
├─ getUserById(Long)
   ├─ validateId(Long)
   ├─ UserRepository.findById(Long)
   └─ processUserData(User)
      ├─ User.setLastAccessed(Long)
      └─ logUserAccess(Long)
```

## 限制和注意事项

1. **类型推断**: 当前版本使用简化的类型推断，复杂的泛型可能无法完全解析
2. **动态调用**: 反射调用和动态代理无法静态分析
3. **Lambda表达式**: 部分Lambda表达式可能无法完全追踪
4. **性能**: 大型项目可能需要较长分析时间

## 故障排除

### 常见问题

1. **"未找到方法"**: 检查文件路径和方法名是否正确
2. **"项目加载失败"**: 确保项目包含Java源文件
3. **"渲染失败"**: 检查Graphviz是否正确安装

### 调试模式

添加`--verbose`参数获取详细日志：
```bash
python run.py --verbose <其他参数>
```

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
