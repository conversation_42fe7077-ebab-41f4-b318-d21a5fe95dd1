"""
过滤和筛选逻辑
排除JDK、Spring框架和外部库的方法调用
"""

import re
from typing import List, Set, Dict
from .config import EXCLUDED_PACKAGES, SPRING_ANNOTATIONS
from .java_parser import MethodCall, MethodInfo, ClassInfo


class CallFilter:
    """调用过滤器"""
    
    def __init__(self, project_packages: Set[str]):
        self.project_packages = project_packages
        self.excluded_patterns = self._build_exclusion_patterns()
    
    def _build_exclusion_patterns(self) -> List[re.Pattern]:
        """构建排除模式的正则表达式"""
        patterns = []
        
        # 基础排除包
        for package in EXCLUDED_PACKAGES:
            pattern = re.compile(rf'^{re.escape(package)}.*')
            patterns.append(pattern)
        
        # 常见的工具类和框架类
        additional_patterns = [
            re.compile(r'^java\..*'),
            re.compile(r'^javax\..*'),
            re.compile(r'^org\.springframework\..*'),
            re.compile(r'^org\.apache\..*'),
            re.compile(r'^com\.fasterxml\..*'),
            re.compile(r'^org\.slf4j\..*'),
            re.compile(r'^ch\.qos\.logback\..*'),
            re.compile(r'^org\.hibernate\..*'),
            re.compile(r'^org\.mybatis\..*'),
            re.compile(r'^com\.mysql\..*'),
            re.compile(r'^org\.postgresql\..*'),
            re.compile(r'^redis\.clients\..*'),
            re.compile(r'^org\.mongodb\..*'),
            re.compile(r'^com\.alibaba\..*'),
            re.compile(r'^com\.google\..*'),
            re.compile(r'^org\.json\..*'),
            re.compile(r'^lombok\..*'),
        ]
        
        patterns.extend(additional_patterns)
        return patterns
    
    def is_project_method(self, class_name: str) -> bool:
        """判断是否为项目内的方法"""
        if not class_name or class_name == "Unknown":
            return False
        
        # 检查是否匹配排除模式
        for pattern in self.excluded_patterns:
            if pattern.match(class_name):
                return False
        
        # 检查是否为项目包
        for project_package in self.project_packages:
            if class_name.startswith(project_package):
                return True
        
        # 如果没有包信息，检查是否为简单类名
        if '.' not in class_name:
            return True
        
        return False
    
    def filter_method_calls(self, method_calls: List[MethodCall]) -> List[MethodCall]:
        """过滤方法调用，只保留项目内的调用"""
        filtered_calls = []
        
        for call in method_calls:
            if self.is_project_method(call.called_class):
                filtered_calls.append(call)
        
        return filtered_calls
    
    def is_business_logic_method(self, method_info: MethodInfo) -> bool:
        """判断是否为业务逻辑方法"""
        # 排除getter/setter
        if self._is_getter_setter(method_info.method_name):
            return False
        
        # 排除构造函数
        if method_info.method_name == method_info.class_name:
            return False
        
        # 排除toString, equals, hashCode等
        if method_info.method_name in ['toString', 'equals', 'hashCode', 'clone']:
            return False
        
        return True
    
    def _is_getter_setter(self, method_name: str) -> bool:
        """判断是否为getter/setter方法"""
        return (method_name.startswith('get') or 
                method_name.startswith('set') or 
                method_name.startswith('is'))


class ProjectPackageDetector:
    """项目包检测器"""
    
    @staticmethod
    def detect_project_packages(classes: Dict[str, ClassInfo]) -> Set[str]:
        """自动检测项目的包结构"""
        packages = set()
        
        for class_name, class_info in classes.items():
            if class_info.package:
                # 获取顶级包名
                package_parts = class_info.package.split('.')
                if len(package_parts) >= 2:
                    # 通常项目包结构为 com.company.project
                    top_level_package = '.'.join(package_parts[:3])
                    packages.add(top_level_package)
                else:
                    packages.add(class_info.package)
        
        # 过滤掉明显的框架包
        filtered_packages = set()
        for package in packages:
            if not any(package.startswith(excluded) for excluded in EXCLUDED_PACKAGES):
                filtered_packages.add(package)
        
        return filtered_packages


class SpringBootMethodFilter:
    """Spring Boot特定的方法过滤器"""
    
    def __init__(self):
        self.spring_method_patterns = [
            re.compile(r'.*Controller$'),
            re.compile(r'.*Service$'),
            re.compile(r'.*Repository$'),
            re.compile(r'.*Component$'),
            re.compile(r'.*Configuration$'),
        ]
    
    def is_spring_boot_business_method(self, method_info: MethodInfo, class_info: ClassInfo) -> bool:
        """判断是否为Spring Boot业务方法"""
        # 检查类是否为Spring Boot组件
        if not self._is_spring_component(class_info):
            return True  # 非Spring组件的方法都认为是业务方法
        
        # Spring Boot特定的过滤逻辑
        if method_info.method_name in ['main', 'run']:
            return False
        
        # 配置类的Bean方法
        if self._is_configuration_class(class_info) and self._has_bean_annotation(method_info):
            return False
        
        return True
    
    def _is_spring_component(self, class_info: ClassInfo) -> bool:
        """检查是否为Spring组件"""
        # 这里需要更复杂的注解检测逻辑
        # 简化版本：根据类名模式判断
        for pattern in self.spring_method_patterns:
            if pattern.match(class_info.name):
                return True
        return False
    
    def _is_configuration_class(self, class_info: ClassInfo) -> bool:
        """检查是否为配置类"""
        return class_info.name.endswith('Configuration') or class_info.name.endswith('Config')
    
    def _has_bean_annotation(self, method_info: MethodInfo) -> bool:
        """检查方法是否有Bean注解（简化版本）"""
        # 实际实现需要解析注解
        return method_info.method_name.startswith('create') or method_info.method_name.endswith('Bean')
