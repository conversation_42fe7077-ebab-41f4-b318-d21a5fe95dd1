"""
流程图生成器
使用graphviz生成可视化流程图
"""

import graphviz
import networkx as nx
from typing import List, Dict, Set, Optional
import os
from pathlib import Path

from .call_chain_analyzer import CallChainAnalyzer, CallPath
from .java_parser import MethodInfo
from .config import GRAPH_CONFIG


class FlowchartGenerator:
    """流程图生成器"""
    
    def __init__(self, analyzer: CallChainAnalyzer):
        self.analyzer = analyzer
        self.graph_config = GRAPH_CONFIG.copy()
    
    def generate_flowchart(self, call_paths: List[CallPath], output_path: str, 
                          title: str = "Method Call Chain") -> str:
        """生成流程图"""
        if not call_paths:
            raise ValueError("没有调用路径可以生成流程图")
        
        # 创建Graphviz图
        dot = graphviz.Digraph(comment=title)
        dot.attr(rankdir='TB', size='12,8', dpi=self.graph_config['dpi'])
        dot.attr('node', 
                shape=self.graph_config['node_shape'],
                style=self.graph_config['node_style'],
                fillcolor=self.graph_config['node_fillcolor'],
                fontname=self.graph_config['font_name'],
                fontsize=self.graph_config['font_size'])
        dot.attr('edge',
                color=self.graph_config['edge_color'],
                fontname=self.graph_config['font_name'],
                fontsize=str(int(self.graph_config['font_size']) - 1))
        
        # 收集所有节点和边
        nodes = set()
        edges = set()
        cycles = set()
        
        for call_path in call_paths:
            path_nodes = call_path.path
            path_types = call_path.call_types
            
            # 添加节点
            for node in path_nodes:
                if node != "CYCLE_DETECTED":
                    nodes.add(node)
            
            # 添加边
            for i in range(len(path_nodes) - 1):
                current_node = path_nodes[i]
                next_node = path_nodes[i + 1]
                
                if next_node == "CYCLE_DETECTED":
                    cycles.add(current_node)
                    continue
                
                call_type = path_types[i] if i < len(path_types) else 'direct'
                edges.add((current_node, next_node, call_type))
        
        # 添加节点到图
        for node in nodes:
            node_label = self._format_node_label(node)
            node_color = self._get_node_color(node)
            dot.node(node, label=node_label, fillcolor=node_color)
        
        # 添加边到图
        for source, target, call_type in edges:
            edge_label = self._get_edge_label(call_type)
            edge_style = self._get_edge_style(call_type)
            dot.edge(source, target, label=edge_label, style=edge_style)
        
        # 标记循环节点
        for cycle_node in cycles:
            if cycle_node in nodes:
                dot.node(cycle_node, fillcolor='orange', 
                        label=f"{self._format_node_label(cycle_node)}\\n(CYCLE)")
        
        # 渲染图
        output_file = self._render_graph(dot, output_path, title)
        return output_file
    
    def _format_node_label(self, method_key: str) -> str:
        """格式化节点标签"""
        if '.' in method_key:
            class_name, method_name = method_key.rsplit('.', 1)
            # 简化类名（只显示最后一部分）
            simple_class_name = class_name.split('.')[-1]
            
            # 获取方法参数信息
            if method_key in self.analyzer.parser.methods:
                method_info = self.analyzer.parser.methods[method_key]
                params = ', '.join(method_info.parameters) if method_info.parameters else ''
                return f"{simple_class_name}\\n{method_name}({params})"
            else:
                return f"{simple_class_name}\\n{method_name}()"
        else:
            return method_key
    
    def _get_node_color(self, method_key: str) -> str:
        """获取节点颜色"""
        if method_key in self.analyzer.parser.methods:
            method_info = self.analyzer.parser.methods[method_key]
            
            # 根据方法类型设置颜色
            if method_info.is_static:
                return 'lightgreen'
            elif method_info.is_abstract:
                return 'lightyellow'
            else:
                return 'lightblue'
        
        return 'lightgray'
    
    def _get_edge_label(self, call_type: str) -> str:
        """获取边标签"""
        labels = {
            'direct': '',
            'interface': 'interface',
            'inheritance': 'inherit',
            'polymorphic': 'poly',
            'overload': 'overload',
            'cycle': 'CYCLE'
        }
        return labels.get(call_type, call_type)
    
    def _get_edge_style(self, call_type: str) -> str:
        """获取边样式"""
        styles = {
            'direct': 'solid',
            'interface': 'dashed',
            'inheritance': 'dotted',
            'polymorphic': 'bold',
            'overload': 'solid',
            'cycle': 'bold'
        }
        return styles.get(call_type, 'solid')
    
    def _render_graph(self, dot: graphviz.Digraph, output_path: str, title: str) -> str:
        """渲染图形到文件"""
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        base_name = os.path.splitext(os.path.basename(output_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}.{self.graph_config['format']}")

        try:
            # 渲染图形
            dot.render(output_path, format=self.graph_config['format'], cleanup=True)
            print(f"流程图已生成: {output_file}")
            return output_file
        except Exception as e:
            print(f"渲染图形失败: {e}")
            print("正在生成文本版本的流程图...")

            # 生成文本版本
            text_output = f"{output_path}_text.txt"
            self._generate_text_flowchart(dot, text_output, title)
            return text_output

    def _generate_text_flowchart(self, dot: graphviz.Digraph, output_path: str, title: str):
        """生成文本版本的流程图"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(f"=== {title} ===\n\n")
            f.write("DOT源代码:\n")
            f.write(dot.source)
            f.write("\n\n注意: 需要安装Graphviz系统工具才能生成图像文件\n")
            f.write("安装说明: https://graphviz.org/download/\n")

        print(f"文本版流程图已生成: {output_path}")

    def generate_summary_report(self, call_paths: List[CallPath]) -> str:
        """生成调用链摘要报告"""
        if not call_paths:
            return "没有找到调用路径"
        
        report = []
        report.append("=== 方法调用链分析报告 ===\n")
        
        # 统计信息
        total_paths = len(call_paths)
        max_depth = max(path.depth for path in call_paths) if call_paths else 0
        unique_methods = set()
        cycles_detected = 0
        
        for path in call_paths:
            unique_methods.update(path.path)
            if "CYCLE_DETECTED" in path.path:
                cycles_detected += 1
        
        report.append(f"总调用路径数: {total_paths}")
        report.append(f"最大调用深度: {max_depth}")
        report.append(f"涉及方法数: {len(unique_methods)}")
        report.append(f"检测到循环: {cycles_detected}")
        report.append("")
        
        # 详细路径信息
        report.append("=== 详细调用路径 ===")
        for i, path in enumerate(call_paths[:10], 1):  # 只显示前10条路径
            report.append(f"\n路径 {i} (深度: {path.depth}):")
            for j, method in enumerate(path.path):
                if method == "CYCLE_DETECTED":
                    report.append(f"  {'  ' * j}└─ [循环检测]")
                else:
                    indent = "  " * j
                    arrow = "└─" if j == len(path.path) - 1 else "├─"
                    report.append(f"  {indent}{arrow} {method}")
        
        if total_paths > 10:
            report.append(f"\n... 还有 {total_paths - 10} 条路径未显示")
        
        return "\n".join(report)


class InteractiveFlowchartGenerator(FlowchartGenerator):
    """交互式流程图生成器"""
    
    def generate_interactive_graph(self, call_paths: List[CallPath]) -> nx.DiGraph:
        """生成NetworkX图用于交互式分析"""
        G = nx.DiGraph()
        
        # 添加节点和边
        for call_path in call_paths:
            path_nodes = [node for node in call_path.path if node != "CYCLE_DETECTED"]
            
            # 添加节点
            for node in path_nodes:
                if node not in G:
                    method_info = self.analyzer.parser.methods.get(node)
                    node_attrs = {
                        'label': self._format_node_label(node),
                        'color': self._get_node_color(node),
                        'method_info': method_info
                    }
                    G.add_node(node, **node_attrs)
            
            # 添加边
            for i in range(len(path_nodes) - 1):
                source = path_nodes[i]
                target = path_nodes[i + 1]
                call_type = call_path.call_types[i] if i < len(call_path.call_types) else 'direct'
                
                edge_attrs = {
                    'call_type': call_type,
                    'label': self._get_edge_label(call_type),
                    'style': self._get_edge_style(call_type)
                }
                G.add_edge(source, target, **edge_attrs)
        
        return G
    
    def export_to_formats(self, call_paths: List[CallPath], base_output_path: str) -> Dict[str, str]:
        """导出到多种格式"""
        outputs = {}
        
        # PNG格式
        png_path = f"{base_output_path}.png"
        outputs['png'] = self.generate_flowchart(call_paths, png_path.replace('.png', ''))
        
        # SVG格式（可缩放）
        self.graph_config['format'] = 'svg'
        svg_path = f"{base_output_path}.svg"
        outputs['svg'] = self.generate_flowchart(call_paths, svg_path.replace('.svg', ''))
        
        # DOT源文件
        dot_path = f"{base_output_path}.dot"
        outputs['dot'] = self._generate_dot_source(call_paths, dot_path)
        
        # 重置格式
        self.graph_config['format'] = 'png'
        
        return outputs
    
    def _generate_dot_source(self, call_paths: List[CallPath], output_path: str) -> str:
        """生成DOT源文件"""
        dot = graphviz.Digraph()
        
        # 构建图（简化版本）
        nodes = set()
        edges = set()
        
        for call_path in call_paths:
            for i, node in enumerate(call_path.path):
                if node != "CYCLE_DETECTED":
                    nodes.add(node)
                    if i < len(call_path.path) - 1 and call_path.path[i + 1] != "CYCLE_DETECTED":
                        edges.add((node, call_path.path[i + 1]))
        
        for node in nodes:
            dot.node(node, label=self._format_node_label(node))
        
        for source, target in edges:
            dot.edge(source, target)
        
        # 保存DOT源文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(dot.source)
        
        return output_path
